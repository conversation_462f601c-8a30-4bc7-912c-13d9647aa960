import { defineStore } from 'pinia';
import request from '/@/utils/request';

interface DefIndexState {
  url: string;
  component: string;
}

export const useDefIndexStore = defineStore({
  id: 'defIndex',
  state: (): DefIndexState => ({
    url: '',
    component: '',
  }),
  getters: {},
  actions: {
    /**
     * 查询默认主页配置
     */
    async query() {
      try {
        const response = await defIndexApi.query();
        // 处理不同的响应格式
        if (response && response.data) {
          // 如果是标准格式 {code: 200, data: {...}}
          if (response.data.code === 200 && response.data.data) {
            this.url = response.data.data.url || '';
            this.component = response.data.data.component || '';
          }
          // 如果直接返回数据
          else if (response.data.url) {
            this.url = response.data.url || '';
            this.component = response.data.component || '';
          }
        }
        // 如果响应直接是数据对象
        else if (response && response.url) {
          this.url = response.url || '';
          this.component = response.component || '';
        }
      } catch (error: any) {
        // 如果是序列化错误，暂时跳过查询，使用默认值
        if (error.message && error.message.includes('serialVersionUID')) {
          this.url = '';
          this.component = '';
          return; // 直接返回，不抛出错误
        }

        // 其他错误也设置默认值
        this.url = '';
        this.component = '';
      }
    },

    /**
     * 更新默认主页配置
     */
    async update(url: string, component: string, isRoute: boolean) {
      try {
        const response = await defIndexApi.update(url, component, isRoute);
        // 直接更新状态，避免重新查询时的序列化错误
        this.url = url;
        this.component = component;

        return response;
      } catch (error: any) {
        console.error('更新默认首页配置失败:', error);
        throw error;
      }
    },

    /**
     * 检查是否为默认首页
     */
    check(url: string) {

      return url === this.url.split('/').pop();
    }
  }
});

/**
 * 默认首页配置API
 */
export const defIndexApi = {
  /**
   * 查询默认首页配置
   */
  async query() {
    return await request({
      url: '/sys/sysRoleIndex/queryDefIndex',
      method: 'get',
      params: {
        _t: Date.now(), // 添加时间戳绕过缓存
        noCache: true   // 明确指示不使用缓存
      },
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });
  },

  /**
   * 更新默认首页配置
   */
  async update(url: string, component: string, isRoute: boolean) {
    const data = {
      url: url,
      component: component,
      isRoute: isRoute
    };

    return await request({
      url: '/sys/sysRoleIndex/updateDefIndex',
      method: 'put',
      data: data,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },

  /**
   * 根据角色编码查询
   */
  async queryByCode(roleCode: string) {
    return await request({
      url: '/sys/sysRoleIndex/queryByCode',
      method: 'get',
      params: { roleCode }
    });
  }
};
