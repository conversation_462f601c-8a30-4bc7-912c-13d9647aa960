#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库管理 API

提供知识库的CRUD操作接口，基于Java token认证系统进行权限控制
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status

from backend.common.log import log as logger
from backend.app.iot.schema.knowledge_base import (
    KnowledgeBaseCreate,
    KnowledgeBaseQuery,
    KnowledgeBaseUpdate,
    KnowledgeBaseDelete,
    KnowledgeRetrievalRequest,
    KnowledgeRetrievalResponse
)
from backend.app.iot.service.knowledge_base_service import knowledge_base_service
from backend.common.response.response_schema import ResponseModel, response_base
from backend.common.response.response_code import CustomResponse
# 权限控制已迁移到中间件，不再需要这些导入
# from backend.common.security.jwt import DependsJwtAuth
# from backend.common.security.java_permission import require_java_permission


async def get_current_user_from_request(request: Request) -> int:
    """
    从请求中获取当前用户ID (统一的用户获取方式)

    :param request: HTTP请求对象
    :return: 用户ID
    :raises HTTPException: 如果用户未认证
    """
    # 从认证中间件设置的用户信息中获取
    if hasattr(request, 'user') and hasattr(request.user, 'id'):
        return request.user.id

    # 从请求状态中获取
    if hasattr(request.state, 'user_id'):
        return request.state.user_id

    # 从Java认证信息中获取
    if hasattr(request.state, 'java_user') and 'userId' in request.state.java_user:
        return request.state.java_user['userId']

    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="用户未认证"
    )

router = APIRouter()


@router.get(
    '/health',
    summary='知识库服务健康检查',
    response_model=ResponseModel,
    name='iot_kb_health_check'
)
async def iot_kb_health_check() -> ResponseModel:
    """
    检查知识库服务（RAGFlow）的连接状态

    不需要认证，用于系统监控
    """
    try:
        health_status = await knowledge_base_service.health_check()

        if health_status["status"] == "healthy":
            return response_base.success(
                res=CustomResponse(200, "知识库服务正常"),
                data=health_status
            )
        else:
            return response_base.fail(
                res=CustomResponse(500, "知识库服务异常"),
                data=health_status
            )

    except Exception as e:
        logger.error(f"知识库健康检查失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"健康检查失败: {str(e)}")
        )


@router.get(
    '/list',
    summary='获取知识库列表',
    response_model=ResponseModel
)
async def list_knowledge_bases(
    query: KnowledgeBaseQuery = Depends()
) -> ResponseModel:
    """
    获取知识库列表

    权限要求: knowledge:base:list (由中间件验证)
    """
    try:
        result = await knowledge_base_service.list_knowledge_bases(query)

        return response_base.success(
            res=CustomResponse(200, "获取知识库列表成功"),
            data=result.get("data", [])  # ✅ 直接返回RAGFlow数据
        )

    except HTTPException as e:
        logger.error(f"获取知识库列表失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取知识库列表失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取知识库列表失败: {str(e)}")
        )


@router.post(
    '',
    summary='创建知识库',
    response_model=ResponseModel
)
async def create_knowledge_base(
    kb_data: KnowledgeBaseCreate
) -> ResponseModel:
    """
    创建新的知识库 - 权限由中间件统一验证

    权限要求: knowledge:base:create
    """
    try:
        result = await knowledge_base_service.create_knowledge_base(kb_data)

        return response_base.success(
            res=CustomResponse(200, "创建知识库成功"),
            data=result.get("data")  # ✅ 直接返回RAGFlow数据
        )

    except HTTPException as e:
        logger.error(f"创建知识库失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"创建知识库失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"创建知识库失败: {str(e)}")
        )


# 向后兼容性路由 - 保持原有的 /create 路径可用
@router.post(
    '/create',
    summary='创建知识库 (兼容性接口)',
    response_model=ResponseModel,
    deprecated=True
)
async def create_knowledge_base_legacy(
    kb_data: KnowledgeBaseCreate
) -> ResponseModel:
    """
    创建新的知识库 - 向后兼容接口

    此接口已弃用，请使用 POST /api/iot/v1/knowledge-base 接口
    权限要求: knowledge:base:create (由中间件验证)
    """
    # 直接调用主接口函数
    return await create_knowledge_base(kb_data)


@router.get(
    '/{kb_id}',
    summary='获取知识库详情',
    response_model=ResponseModel
)
async def get_knowledge_base_detail(
    kb_id: str
) -> ResponseModel:
    """
    获取指定知识库的详细信息

    权限要求: knowledge:base:view (由中间件验证)
    """
    try:
        result = await knowledge_base_service.get_knowledge_base(kb_id)

        return response_base.success(
            res=CustomResponse(200, "获取知识库详情成功"),
            data=result.get("data")  # ✅ 直接返回RAGFlow数据
        )

    except HTTPException as e:
        logger.error(f"获取知识库详情失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取知识库详情失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取知识库详情失败: {str(e)}")
        )


@router.put(
    '/{kb_id}',
    summary='更新知识库',
    response_model=ResponseModel
)
async def update_knowledge_base(
    kb_id: str,
    kb_data: KnowledgeBaseUpdate
) -> ResponseModel:
    """
    更新指定知识库的信息

    权限要求: knowledge:base:update (由中间件验证)
    """
    try:
        result = await knowledge_base_service.update_knowledge_base(kb_id, kb_data)

        return response_base.success(
            res=CustomResponse(200, "更新知识库成功"),
            data=result.get("data")  # ✅ 直接返回RAGFlow数据
        )

    except HTTPException as e:
        logger.error(f"更新知识库失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"更新知识库失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"更新知识库失败: {str(e)}")
        )


@router.delete(
    '/delete',
    summary='删除知识库',
    response_model=ResponseModel
)
async def delete_knowledge_bases(
    delete_data: KnowledgeBaseDelete
) -> ResponseModel:
    """
    删除指定的知识库

    权限要求: knowledge:base:delete (由中间件验证)
    """
    try:
        result = await knowledge_base_service.delete_knowledge_bases(delete_data.ids)

        return response_base.success(
            res=CustomResponse(200, "删除知识库成功"),
            data=result.get("data")  # ✅ 直接返回RAGFlow数据
        )

    except HTTPException as e:
        logger.error(f"删除知识库失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"删除知识库失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"删除知识库失败: {str(e)}")
        )


# 嵌入模型使用固定配置，不再提供选择API


@router.get(
    '/stats/overview',
    summary='获取知识库统计信息',
    response_model=ResponseModel
)
async def get_knowledge_base_stats() -> ResponseModel:
    """
    获取知识库的统计信息

    权限要求: knowledge:base:stats (由中间件验证)
    """
    try:
        stats = await knowledge_base_service.get_knowledge_base_stats()

        return response_base.success(
            res=CustomResponse(200, "获取统计信息成功"),
            data=stats  # ✅ 直接返回统计数据
        )

    except HTTPException as e:
        logger.error(f"获取统计信息失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取统计信息失败: {str(e)}")
        )


# ==================== 知识库检索相关接口 ====================

@router.post(
    '/retrieval',
    summary='知识库检索',
    response_model=ResponseModel,
    name='knowledge_base_retrieval'
)
async def knowledge_base_retrieval(
    retrieval_request: KnowledgeRetrievalRequest
) -> ResponseModel:
    """
    知识库检索

    权限要求: knowledge:base:search (由中间件验证)
    """
    try:
        result = await knowledge_base_service.retrieve_knowledge(retrieval_request)

        return response_base.success(
            res=CustomResponse(200, "检索成功"),
            data=result.get("data")  # ✅ 直接返回RAGFlow数据
        )

    except HTTPException as e:
        logger.error(f"知识库检索失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"知识库检索失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"知识库检索失败: {str(e)}")
        )


@router.get(
    '/retrieval/history',
    summary='获取检索历史',
    response_model=ResponseModel,
    name='knowledge_base_retrieval_history'
)
async def get_retrieval_history(
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页大小")
) -> ResponseModel:
    """
    获取用户检索历史

    权限要求: knowledge:base:history (由中间件验证)
    注意：这是一个扩展功能，当前返回空数据
    """
    try:
        # 从JWT token中获取用户ID
        # TODO: 实现从token中解析用户ID的逻辑
        user_id = "current_user"  # 临时占位符

        result = await knowledge_base_service.get_retrieval_history(
            user_id=user_id,
            page=page,
            page_size=page_size
        )

        return response_base.success(
            res=CustomResponse(200, "获取检索历史成功"),
            data=result.get("data")
        )

    except HTTPException as e:
        logger.error(f"获取检索历史失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取检索历史失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取检索历史失败: {str(e)}")
        )
