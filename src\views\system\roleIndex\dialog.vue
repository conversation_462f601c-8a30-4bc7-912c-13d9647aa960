<template>
	<div class="system-role-index-dialog-container">
		<el-dialog :title="state.dialog.title" v-model="state.dialog.isShowDialog" width="769px">
			<el-form ref="roleIndexDialogFormRef" :model="state.ruleForm" size="default" label-width="90px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="角色编码" prop="roleCode" :rules="[{ required: true, message: '角色编码不能为空', trigger: 'blur' }]">
							<el-select v-model="state.ruleForm.roleCode" placeholder="请选择角色" clearable style="width: 100%">
								<el-option v-for="role in state.roleList" :key="role.roleCode" :label="role.roleName" :value="role.roleCode" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="首页URL" prop="url" :rules="[{ required: true, message: '首页URL不能为空', trigger: 'blur' }]">
							<el-input v-model="state.ruleForm.url" placeholder="请输入首页URL" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="组件路径" prop="component" :rules="[{ required: true, message: '组件路径不能为空', trigger: 'blur' }]">
							<el-input v-model="state.ruleForm.component" placeholder="请输入组件路径" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="优先级" prop="priority">
							<el-input-number v-model="state.ruleForm.priority" :min="0" :max="999" placeholder="请输入优先级" style="width: 100%"></el-input-number>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="状态" prop="status">
							<el-radio-group v-model="state.ruleForm.status">
								<el-radio v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictValue">{{ dict.dictLabel }}</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">取 消</el-button>
					<el-button type="primary" @click="onSubmit" size="default">{{ state.dialog.submitTxt }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemRoleIndexDialog">
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { addRoleIndex, updateRoleIndex, getRoleList } from '/@/api/system/roleIndex';
import { useDictStore } from '/@/stores/dictStore';

const dictStore = useDictStore();

interface statusOption {
	dictValue: string;
	dictLabel: string;
	listClass: string;
	cssClass: string;
}

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const roleIndexDialogFormRef = ref();
const state = reactive({
	ruleForm: {
		id: '',
		roleCode: '',
		url: '',
		component: '',
		priority: 0,
		status: '0',
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
	roleList: [] as any[],
});

// 状态列表
const statuslist = ref<statusOption[]>([]);

// 获取状态数据
const getdictdata = async () => {
	try {
		statuslist.value = await dictStore.fetchDict('sys_normal_disable');
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
};

// 获取角色列表
const getRoleListData = async () => {
	try {
		const response = await getRoleList();
		state.roleList = response.data;
	} catch (error) {
		console.error('获取角色列表失败:', error);
	}
};

// 打开弹窗
const openDialog = (type: string, row?: any) => {
	if (type === 'edit') {
		state.ruleForm = { ...row };
		state.dialog.title = '修改角色首页配置';
		state.dialog.submitTxt = '修 改';
	} else {
		state.dialog.title = '新增角色首页配置';
		state.dialog.submitTxt = '新 增';
		// 重置表单
		state.ruleForm = {
			id: '',
			roleCode: '',
			url: '',
			component: '',
			priority: 0,
			status: '0',
		};
	}
	state.dialog.type = type;
	state.dialog.isShowDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	state.dialog.isShowDialog = false;
};

// 取消
const onCancel = () => {
	closeDialog();
};

// 提交
const onSubmit = () => {
	roleIndexDialogFormRef.value.validate(async (valid: boolean) => {
		if (!valid) return;
		try {
			if (state.dialog.type === 'edit') {
				await updateRoleIndex(state.ruleForm);
				ElMessage.success('修改成功');
			} else {
				await addRoleIndex(state.ruleForm);
				ElMessage.success('新增成功');
			}
			closeDialog();
			emit('refresh');
		} catch (error) {
			ElMessage.error(state.dialog.type === 'edit' ? '修改失败' : '新增失败');
		}
	});
};

// 页面加载时
onMounted(() => {
	getdictdata();
	getRoleListData();
});

// 暴露变量
defineExpose({
	openDialog,
});
</script>
