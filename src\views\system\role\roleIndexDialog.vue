<template>
  <el-dialog v-model="dialogVisible" title="首页配置" width="40%" :before-close="handleClose">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="150px">
      <el-form-item label="角色编码" prop="roleCode">
        <el-input v-model="formData.roleCode" :disabled="true" />
      </el-form-item>
      <el-form-item label="首页路由" prop="url">
        <el-input v-model="formData.url" placeholder="请输入首页路由的访问地址" />
        <div class="form-help-text">首页路由的访问地址</div>
      </el-form-item>
      <el-form-item label="组件地址" prop="component">
        <el-input v-model="formData.component" placeholder="请输入前端组件" />
        <div class="form-help-text">首页路由的组件地址</div>
      </el-form-item>
      <el-form-item label="是否路由菜单" prop="route">
        <el-switch v-model="formData.route" />
        <div class="form-help-text">非路由菜单设置成首页，需开启</div>
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-input-number v-model="formData.priority" :min="0" :max="999" style="width: 100%" />
      </el-form-item>
      <el-form-item label="是否开启" prop="status">
        <el-switch v-model="formData.status" active-value="1" inactive-value="0" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">{{ isUpdate ? '更 新' : '新 增' }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { saveOrUpdateRoleIndex, queryIndexByCode } from '/@/api/system/role';

// Emits声明
const emit = defineEmits(['success']);

const dialogVisible = ref(false);
const loading = ref(false);
const isUpdate = ref(false);
const formRef = ref();

const formData = reactive({
  id: '',
  roleCode: '',
  url: '',
  component: '',
  route: true,
  priority: 0,
  status: '1'
});

const rules = {
  url: [
    { required: true, message: '首页路由不能为空', trigger: 'blur' }
  ],
  component: [
    { required: true, message: '组件地址不能为空', trigger: 'blur' }
  ]
};

// 打开对话框
const openDialog = async (data: { roleCode: string }) => {
  dialogVisible.value = true;
  loading.value = false;
  
  // 重置表单
  Object.assign(formData, {
    id: '',
    roleCode: data.roleCode,
    url: '',
    component: '',
    route: true,
    priority: 0,
    status: '1'
  });
  
  // 查询现有配置
  try {
    const res = await queryIndexByCode({ roleCode: data.roleCode });
    res.result = res.data.data;
    isUpdate.value = !!res.result?.id;
    if (isUpdate.value) {
      Object.assign(formData, res.result);
    }
  } catch (error) {
    console.error('查询角色首页配置失败:', error);
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  formRef.value?.resetFields();
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;

    // 验证必填字段
    if (!formData.roleCode || !formData.url || !formData.component) {
      ElMessage.error('角色编码、首页路由和组件地址不能为空');
      return;
    }

    // 构建提交数据，确保正确的数据类型
    const submitData = {
      id: formData.id || '',
      roleCode: formData.roleCode,
      url: formData.url,
      component: formData.component,
      route: formData.route !== undefined ? formData.route : true,
      priority: Number(formData.priority) || 0,
      status: Number(formData.status) || 1  // 确保status是数字类型
    };

    console.log('提交数据 (JSON格式):', JSON.stringify(submitData, null, 2));
    await saveOrUpdateRoleIndex(submitData, isUpdate.value);
    ElMessage.success(isUpdate.value ? '更新成功' : '新增成功');
    handleClose();
    emit('success', { isUpdate: isUpdate.value, values: submitData });
  } catch (error: any) {
    console.error('提交失败:', error);
    ElMessage.error(isUpdate.value ? '更新失败' : '新增失败');
  } finally {
    loading.value = false;
  }
};

// 暴露方法
defineExpose({
  openDialog
});
</script>

<style scoped>
.form-help-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
</style>
