<template>
  <div class="device-container">
    <div class="table-header">
      <h2>设备列表</h2>
      <div>
        <el-button type="danger" :disabled="selectedIds.length === 0" @click="handleBatchDelete" style="margin-right: 8px;">
          批量删除
        </el-button>
        <el-button type="primary" @click="showAddDialog">
          <el-icon><Plus /></el-icon>
          新增设备
        </el-button>
      </div>
    </div>

    <el-table
      ref="tableRef"
      :data="tableData"
      stripe
      border
      style="width: 100%"
      v-loading="loading"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择 -->
      <el-table-column type="selection" width="48" align="center" />
      <!-- 行号 -->
      <el-table-column
        type="index"
        label="行号"
        width="80"
        align="center"
      />

      <!-- 名称 -->
      <el-table-column
        prop="name"
        label="名称"
        min-width="160"
        show-overflow-tooltip
      />

      <!-- 描述 -->
      <el-table-column
        prop="description"
        label="描述"
        min-width="180"
        show-overflow-tooltip
      />

      <!-- 通道 -->
      <el-table-column
        prop="channelName"
        label="通道"
        min-width="140"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ row.channelName || row.channelId || '-' }}
        </template>
      </el-table-column>

      <!-- 设备使能 -->
      <el-table-column
        prop="enable"
        label="设备使能"
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <el-tag :type="row.enable ? 'success' : 'info'">
            {{ row.enable ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 活跃时间 -->
      <el-table-column
        prop="activeTime"
        label="活跃时间"
        min-width="180"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ formatDateTime(row.activeTime) }}
        </template>
      </el-table-column>

      <!-- 设备状态 -->
      <el-table-column
        prop="deviceStatus"
        label="设备状态"
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <el-tag :type="getStatusTag(row)">
            {{ getStatusText(row) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 变量数量 -->
      <el-table-column
        prop="variableCount"
        label="变量数量"
        width="120"
        align="center"
      />

      <!-- 插件名称 -->
      <el-table-column
        prop="pluginName"
        label="插件名称"
        min-width="220"
        show-overflow-tooltip
      />

      <!-- 操作 -->
      <el-table-column
        label="操作"
        width="180"
        align="center"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-divider direction="vertical" />
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增设备弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="新建窗口"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-tabs v-model="activeTab" class="device-tabs">
        <el-tab-pane label="设备信息" name="device">
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="140px"
            class="device-form"
          >
            <!-- 基础信息 -->
            <div class="form-section">
              <h3 class="section-title">基础信息</h3>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="名称" prop="name">
                    <el-input
                      v-model="formData.name"
                      placeholder="请输入..."
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="描述" prop="description">
                    <el-input
                      v-model="formData.description"
                      placeholder="请输入..."
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="设备使能" prop="enable">
                    <el-switch
                      v-model="formData.enable"
                      active-text="启用"
                      inactive-text="禁用"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="日志等级" prop="logLevel">
                    <el-select
                      v-model="formData.logLevel"
                      placeholder="请选择..."
                      style="width: 100%"
                    >
                      <el-option label="Trace" value="Trace" />
                      <el-option label="Debug" value="Debug" />
                      <el-option label="Info" value="Info" />
                      <el-option label="Warn" value="Warn" />
                      <el-option label="Error" value="Error" />
                      <el-option label="Fatal" value="Fatal" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 连接 -->
            <div class="form-section">
              <h3 class="section-title">连接</h3>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="通道" prop="channelId">
                    <div class="channel-select">
                      <el-select
                        v-model="formData.channelId"
                        placeholder="请选择..."
                        style="width: 100%"
                        filterable
                        clearable
                        @change="onChannelChanged"
                      >
                        <el-option
                          v-for="channel in channelList"
                          :key="channel.id"
                          :label="channel.name"
                          :value="channel.id"
                        >
                          <span>{{ channel.name }}</span>
                          <span class="channel-desc">{{ channel.description || '' }}</span>
                        </el-option>
                      </el-select>
                      <el-button
                        type="primary"
                        size="small"
                        class="add-channel-btn"
                        @click="handleAddChannel"
                      >
                        <el-icon><Plus /></el-icon>
                      </el-button>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="默认执行间隔" prop="intervalTime">
                    <el-input
                      v-model="formData.intervalTime"
                      placeholder="请输入..."
                      clearable
                    >
                      <template #append>ms</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 冗余 -->
            <div class="form-section">
              <h3 class="section-title">冗余</h3>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="启用冗余" prop="redundantEnable">
                    <el-switch
                      v-model="formData.redundantEnable"
                      active-text="启用"
                      inactive-text="禁用"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="冗余操作模式" prop="redundantSwitchType">
                    <el-select
                      v-model="formData.redundantSwitchType"
                      placeholder="请选择..."
                      style="width: 100%"
                      :disabled="!formData.redundantEnable"
                    >
                      <el-option label="OffLine" :value="0" />
                      <el-option label="OnLine" :value="1" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="冗余设备" prop="redundantDeviceId">
                    <el-select
                      v-model="formData.redundantDeviceId"
                      placeholder="请选择..."
                      style="width: 100%"
                      :disabled="!formData.redundantEnable"
                      clearable
                    >
                      <el-option
                        v-for="device in deviceList"
                        :key="device.id"
                        :label="device.name"
                        :value="device.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="冗余检测时间" prop="redundantScanIntervalTime">
                    <el-input
                      v-model="formData.redundantScanIntervalTime"
                      placeholder="请输入..."
                      :disabled="!formData.redundantEnable"
                      clearable
                    >
                      <template #append>ms</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="冗余检测脚本" prop="redundantScript">
                    <el-input
                      v-model="formData.redundantScript"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入..."
                      :disabled="!formData.redundantEnable"
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 备用 -->
            <div class="form-section">
              <h3 class="section-title">备用</h3>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="备用1" prop="remark1">
                    <el-input
                      v-model="formData.remark1"
                      placeholder="请输入..."
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="备用2" prop="remark2">
                    <el-input
                      v-model="formData.remark2"
                      placeholder="请输入..."
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="备用3" prop="remark3">
                    <el-input
                      v-model="formData.remark3"
                      placeholder="请输入..."
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="备用4" prop="remark4">
                    <el-input
                      v-model="formData.remark4"
                      placeholder="请输入..."
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="备用5" prop="remark5">
                    <el-input
                      v-model="formData.remark5"
                      placeholder="请输入..."
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </el-tab-pane>
        
        <!-- 插件信息标签页 - 动态显示 -->
        <el-tab-pane 
          label="插件信息" 
          name="plugin" 
          v-if="showPluginTab"
        >
          <div class="plugin-form">
            <!-- 调试信息 -->
            <!-- <div class="debug-info" style="background: #f5f7fa; padding: 10px; margin-bottom: 20px; border-radius: 4px;">
              <p><strong>调试信息:</strong></p>
              <p>showPluginTab: {{ showPluginTab }}</p>
              <p>pluginLoading: {{ pluginLoading }}</p>
              <p>当前通道类型: {{ currentChannelType }}</p>
              <p>当前通道插件: {{ formData.channelId ? channelList.find(c => c.id === formData.channelId)?.pluginName : '未选择' }}</p>
              <p>当前通道pluginType: {{ formData.channelId ? channelList.find(c => c.id === formData.channelId)?.pluginType : '未选择' }}</p>
              <p>isCollectChannel: {{ isCollectChannel }}</p>
              <p>pluginProperties数量: {{ Object.keys(pluginProperties).length }}</p>
              <p>pluginPropertyEditorItems数量: {{ pluginPropertyEditorItems.length }}</p>
              <p>pluginProperties内容: {{ JSON.stringify(pluginProperties, null, 2) }}</p>
              <p>pluginPropertyEditorItems内容: {{ JSON.stringify(pluginPropertyEditorItems, null, 2) }}</p>
              
              手动测试按钮
              <div style="margin-top: 10px;">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="testPluginProperties"
                  :disabled="!formData.channelId"
                >
                  手动测试插件属性加载
                </el-button>
                <el-button 
                  type="warning" 
                  size="small" 
                  @click="clearPluginData"
                  style="margin-left: 10px;"
                >
                  清空插件数据
                </el-button>
              </div>
            </div> -->
            
            <!-- 采集通道配置界面 -->
            <div v-if="isCollectChannel" class="collect-channel-config">
              <h3 class="section-title">采集通道配置</h3>
              <el-form
                ref="collectFormRef"
                :model="collectChannelForm"
                :rules="collectChannelRules"
                label-width="140px"
                class="collect-channel-form"
              >
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="协议类型" prop="protocolType">
                      <el-select
                        v-model="collectChannelForm.protocolType"
                        placeholder="请选择..."
                        style="width: 100%"
                      >
                        <el-option label="ModbusTcp" value="ModbusTcp" />
                        <el-option label="ModbusRtu" value="ModbusRtu" />
                        <el-option label="OpcUa" value="OpcUa" />
                        <el-option label="S7" value="S7" />
                        <el-option label="Ethernet" value="Ethernet" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="默认站号" prop="defaultStation">
                      <el-input-number
                        v-model="collectChannelForm.defaultStation"
                        :min="1"
                        :max="255"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="Dtu注册包" prop="dtuRegisterPacket">
                      <el-input
                        v-model="collectChannelForm.dtuRegisterPacket"
                        placeholder="请输入..."
                        clearable
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="最大打包长度" prop="maxPacketLength">
                      <el-input-number
                        v-model="collectChannelForm.maxPacketLength"
                        :min="1"
                        :max="1000"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="读写超时时间" prop="timeout">
                      <el-input-number
                        v-model="collectChannelForm.timeout"
                        :min="100"
                        :max="30000"
                        style="width: 100%"
                      >
                        <template #append>ms</template>
                      </el-input-number>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="发送延时" prop="sendDelay">
                      <el-input-number
                        v-model="collectChannelForm.sendDelay"
                        :min="0"
                        :max="1000"
                        style="width: 100%"
                      >
                        <template #append>ms</template>
                      </el-input-number>
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="字符串反转" prop="stringReverse">
                      <el-switch
                        v-model="collectChannelForm.stringReverse"
                        active-text="是"
                        inactive-text="否"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="解析规则" prop="parseRule">
                      <el-select
                        v-model="collectChannelForm.parseRule"
                        placeholder="请选择..."
                        style="width: 100%"
                      >
                        <el-option label="ABCD" value="ABCD" />
                        <el-option label="DCBA" value="DCBA" />
                        <el-option label="BADC" value="BADC" />
                        <el-option label="CDAB" value="CDAB" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="失败重试次数" prop="retryCount">
                      <el-input-number
                        v-model="collectChannelForm.retryCount"
                        :min="0"
                        :max="10"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            
            <!-- 业务通道配置界面 -->
            <div v-else-if="!isCollectChannel" class="business-channel-config">
              <h3 class="section-title">业务通道配置</h3>
              <el-form
                ref="businessFormRef"
                :model="businessChannelForm"
                :rules="businessChannelRules"
                label-width="140px"
                class="business-channel-form"
              >
                <!-- 连接配置 -->
                <div class="form-section">
                  <h4 class="subsection-title">连接配置</h4>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="IP地址" prop="ip">
                        <el-input
                          v-model="businessChannelForm.ip"
                          placeholder="请输入IP地址"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="端口" prop="port">
                        <el-input-number
                          v-model="businessChannelForm.port"
                          :min="1"
                          :max="65535"
                          style="width: 100%"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="协议版本" prop="protocolVersion">
                        <el-select
                          v-model="businessChannelForm.protocolVersion"
                          placeholder="请选择协议版本"
                          style="width: 100%"
                        >
                          <el-option label="V311" value="V311" />
                          <el-option label="V5" value="V5" />
                          <el-option label="V3.1.1" value="V3.1.1" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="QoS" prop="qos">
                        <el-select
                          v-model="businessChannelForm.qos"
                          placeholder="请选择QoS"
                          style="width: 100%"
                        >
                          <el-option label="AtMostOnce" value="AtMostOnce" />
                          <el-option label="AtLeastOnce" value="AtLeastOnce" />
                          <el-option label="ExactlyOnce" value="ExactlyOnce" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="用户名" prop="username">
                        <el-input
                          v-model="businessChannelForm.username"
                          placeholder="请输入用户名"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="密码" prop="password">
                        <el-input
                          v-model="businessChannelForm.password"
                          type="password"
                          placeholder="请输入密码"
                          clearable
                          show-password
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="连接ID" prop="connectionId">
                        <el-input
                          v-model="businessChannelForm.connectionId"
                          placeholder="请输入连接ID"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="连接超时时间" prop="connectionTimeout">
                        <el-input-number
                          v-model="businessChannelForm.connectionTimeout"
                          :min="1000"
                          :max="60000"
                          style="width: 100%"
                        >
                          <template #append>ms</template>
                        </el-input-number>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>

                <!-- 功能配置 -->
                <div class="form-section">
                  <h4 class="subsection-title">功能配置</h4>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="TLS" prop="tls">
                        <el-switch
                          v-model="businessChannelForm.tls"
                          active-text="启用"
                          inactive-text="禁用"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="WebSocket" prop="websocket">
                        <el-switch
                          v-model="businessChannelForm.websocket"
                          active-text="启用"
                          inactive-text="禁用"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="WebSocket URL" prop="websocketUrl">
                        <el-input
                          v-model="businessChannelForm.websocketUrl"
                          placeholder="请输入WebSocket URL"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="允许RPC写入" prop="allowRpcWrite">
                        <el-switch
                          v-model="businessChannelForm.allowRpcWrite"
                          active-text="允许"
                          inactive-text="禁止"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="RPC写入主题" prop="rpcWriteTopic">
                        <el-input
                          v-model="businessChannelForm.rpcWriteTopic"
                          placeholder="请输入RPC写入主题"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="RPC请求数据主题" prop="rpcRequestDataTopic">
                        <el-input
                          v-model="businessChannelForm.rpcRequestDataTopic"
                          placeholder="请输入RPC请求数据主题"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>

                <!-- 数据上传配置 -->
                <div class="form-section">
                  <h4 class="subsection-title">数据上传配置</h4>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="变量主题" prop="variableTopic">
                        <el-input
                          v-model="businessChannelForm.variableTopic"
                          placeholder="请输入变量主题"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="设备主题" prop="deviceTopic">
                        <el-input
                          v-model="businessChannelForm.deviceTopic"
                          placeholder="请输入设备主题"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="报警主题" prop="alarmTopic">
                        <el-input
                          v-model="businessChannelForm.alarmTopic"
                          placeholder="请输入报警主题"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="上传模式" prop="uploadMode">
                        <el-select
                          v-model="businessChannelForm.uploadMode"
                          placeholder="请选择上传模式"
                          style="width: 100%"
                        >
                          <el-option label="变化" value="Change" />
                          <el-option label="定时" value="Timed" />
                          <el-option label="手动" value="Manual" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="定时上传间隔" prop="timedUploadInterval">
                        <el-input-number
                          v-model="businessChannelForm.timedUploadInterval"
                          :min="100"
                          :max="3600000"
                          style="width: 100%"
                        >
                          <template #append>ms</template>
                        </el-input-number>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="上传每页条数" prop="uploadItemsPerPage">
                        <el-input-number
                          v-model="businessChannelForm.uploadItemsPerPage"
                          :min="1"
                          :max="10000"
                          style="width: 100%"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>

                <!-- 开关配置 -->
                <div class="form-section">
                  <h4 class="subsection-title">开关配置</h4>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="变量列表上传" prop="variableListUpload">
                        <el-switch
                          v-model="businessChannelForm.variableListUpload"
                          active-text="启用"
                          inactive-text="禁用"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="设备状态列表上传" prop="deviceStatusListUpload">
                        <el-switch
                          v-model="businessChannelForm.deviceStatusListUpload"
                          active-text="启用"
                          inactive-text="禁用"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="报警列表上传" prop="alarmListUpload">
                        <el-switch
                          v-model="businessChannelForm.alarmListUpload"
                          active-text="启用"
                          inactive-text="禁用"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="选择全部变量" prop="selectAllVariables">
                        <el-switch
                          v-model="businessChannelForm.selectAllVariables"
                          active-text="是"
                          inactive-text="否"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="JSON缩进格式化" prop="jsonIndentFormatting">
                        <el-switch
                          v-model="businessChannelForm.jsonIndentFormatting"
                          active-text="启用"
                          inactive-text="禁用"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="启用缓存" prop="enableCache">
                        <el-switch
                          v-model="businessChannelForm.enableCache"
                          active-text="启用"
                          inactive-text="禁用"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="分组上传" prop="groupUpload">
                        <el-switch
                          v-model="businessChannelForm.groupUpload"
                          active-text="启用"
                          inactive-text="禁用"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="过滤离线变量" prop="filterOfflineVariables">
                        <el-switch
                          v-model="businessChannelForm.filterOfflineVariables"
                          active-text="启用"
                          inactive-text="禁用"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="详细日志" prop="detailedLog">
                        <el-switch
                          v-model="businessChannelForm.detailedLog"
                          active-text="启用"
                          inactive-text="禁用"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="缓存文件最大长度" prop="maxCacheFileLength">
                        <el-input-number
                          v-model="businessChannelForm.maxCacheFileLength"
                          :min="1"
                          :max="10000"
                          style="width: 100%"
                        >
                          <template #append>MB</template>
                        </el-input-number>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="内存队列最大数量" prop="maxMemoryQueueSize">
                        <el-input-number
                          v-model="businessChannelForm.maxMemoryQueueSize"
                          :min="1000"
                          :max="1000000"
                          style="width: 100%"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>

                <!-- 证书配置 -->
                <div class="form-section">
                  <h4 class="subsection-title">证书配置</h4>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="CA文件" prop="caFile">
                        <div class="file-input-group">
                          <el-input
                            v-model="businessChannelForm.caFile"
                            placeholder="请选择CA文件"
                            readonly
                            style="flex: 1"
                          />
                          <el-button type="primary" size="small" @click="handleFileSelect('caFile')">
                            浏览
                          </el-button>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="客户端证书" prop="clientCertificate">
                        <div class="file-input-group">
                          <el-input
                            v-model="businessChannelForm.clientCertificate"
                            placeholder="请选择客户端证书"
                            readonly
                            style="flex: 1"
                          />
                          <el-button type="primary" size="small" @click="handleFileSelect('clientCertificate')">
                            浏览
                          </el-button>
                        </div>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="客户端Key文件" prop="clientKeyFile">
                        <div class="file-input-group">
                          <el-input
                            v-model="businessChannelForm.clientKeyFile"
                            placeholder="请选择客户端Key文件"
                            readonly
                            style="flex: 1"
                          />
                          <el-button type="primary" size="small" @click="handleFileSelect('clientKeyFile')">
                            浏览
                          </el-button>
                        </div>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </el-form>
            </div>
            
            <div v-else class="plugin-empty">
              <el-empty description="暂无插件属性配置">
                <p>请检查：</p>
                <p>1. 是否选择了通道</p>
                <p>2. 通道是否有对应的插件</p>
                <p>3. 插件是否有属性配置</p>
              </el-empty>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">
            <el-icon><Close /></el-icon>
            关闭
          </el-button>
          <el-button type="primary" @click="handleSave" :loading="saveLoading">
            <el-icon><Check /></el-icon>
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Close, Check } from '@element-plus/icons-vue'
import { 
  getDeviceList, 
  batchSaveDevice, 
  getChannelList,
  deleteDevice,
  type DevicePayloadItem,
  type ItemChangedType
} from '@/api/gateway/device'

type DeviceRow = {
  id?: number
  name: string
  description?: string
  channelName?: string
  channelId?: number | string
  enable?: boolean
  activeTime?: string | number | Date | null
  deviceStatus?: number | string
  variableCount?: number
  pluginName?: string
  [key: string]: any
}

type ChannelItem = {
  id: number
  name: string
  description?: string
  pluginName?: string
  pluginType?: number
  [key: string]: any
}

const loading = ref(false)
const saveLoading = ref(false)
const tableData = ref<DeviceRow[]>([])
const channelList = ref<ChannelItem[]>([])
const deviceList = ref<DeviceRow[]>([])
const tableRef = ref()
const selectedIds = ref<number[]>([])

// 弹窗相关
const dialogVisible = ref(false)
const activeTab = ref('device')
const formRef = ref()
const collectFormRef = ref() // 新增：采集通道表单引用
const businessFormRef = ref() // 新增：业务通道表单引用

// 插件相关
const showPluginTab = ref(false)

// 表单数据
const formData = reactive<DevicePayloadItem>({
  id: 0,
  name: '',
  description: '',
  channelId: 0,
  intervalTime: '1000',
  enable: true,
  logLevel: 'Info',
  devicePropertys: {},
  redundantEnable: false,
  redundantDeviceId: null,
  redundantSwitchType: 0,
  redundantScanIntervalTime: 30000,
  redundantScript: '',
  remark1: '',
  remark2: '',
  remark3: '',
  remark4: '',
  remark5: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ],
  channelId: [
    { required: true, message: '请选择通道', trigger: 'change' }
  ],
  intervalTime: [
    { required: true, message: '请输入执行间隔', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ],
  redundantScanIntervalTime: [
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ]
}

// 新增：采集通道表单数据
const collectChannelForm = reactive({
  protocolType: 'ModbusTcp',
  defaultStation: 1,
  dtuRegisterPacket: '',
  maxPacketLength: 100,
  timeout: 3000,
  sendDelay: 0,
  stringReverse: false,
  parseRule: 'ABCD',
  retryCount: 3
})

// 新增：采集通道表单验证规则
const collectChannelRules = {
  protocolType: [
    { required: true, message: '请选择协议类型', trigger: 'change' }
  ],
  defaultStation: [
    { required: true, message: '请输入默认站号', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ],
  maxPacketLength: [
    { required: true, message: '请输入最大打包长度', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ],
  timeout: [
    { required: true, message: '请输入读写超时时间', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ],
  sendDelay: [
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ],
  retryCount: [
    { required: true, message: '请输入失败重试次数', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ]
}

// 新增：业务通道表单数据
const businessChannelForm = reactive({
  // 连接配置
  ip: '127.0.0.1',
  port: 1883,
  protocolVersion: 'V311',
  qos: 'AtMostOnce',
  username: 'admin',
  password: '111111',
  connectionId: 'ThingsGatewayId',
  connectionTimeout: 3000,
  
  // 功能配置
  tls: false,
  websocket: false,
  websocketUrl: 'ws://127.0.0.1:8083/mqtt',
  allowRpcWrite: false,
  rpcWriteTopic: '',
  rpcRequestDataTopic: '',
  
  // 数据上传配置
  variableTopic: 'ThingsGateway/Variable',
  deviceTopic: '',
  alarmTopic: '',
  uploadMode: 'Change',
  timedUploadInterval: 1000,
  uploadItemsPerPage: 2000,
  
  // 开关配置
  variableListUpload: true,
  deviceStatusListUpload: true,
  alarmListUpload: true,
  selectAllVariables: false,
  jsonIndentFormatting: true,
  enableCache: false,
  groupUpload: false,
  filterOfflineVariables: false,
  detailedLog: false,
  maxCacheFileLength: 1024,
  maxMemoryQueueSize: 100000,
  
  // 证书配置
  caFile: '',
  clientCertificate: '',
  clientKeyFile: ''
})

// 新增：业务通道表单验证规则
const businessChannelRules = {
  ip: [
    { required: true, message: '请输入IP地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ],
  protocolVersion: [
    { required: true, message: '请选择协议版本', trigger: 'change' }
  ],
  qos: [
    { required: true, message: '请选择QoS', trigger: 'change' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  connectionId: [
    { required: true, message: '请输入连接ID', trigger: 'blur' }
  ],
  connectionTimeout: [
    { required: true, message: '请输入连接超时时间', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ],
  timedUploadInterval: [
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ],
  uploadItemsPerPage: [
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ],
  maxCacheFileLength: [
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ],
  maxMemoryQueueSize: [
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ]
}

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

function normalizeRow(item: Record<string, any>): DeviceRow {
  return {
    id: item.id ?? item.deviceId,
    name: item.name ?? '-',
    description: item.description ?? item.remark1 ?? item.remark ?? '-',
    channelName: item.channelName ?? item.channel?.name ?? '',
    channelId: item.channelId ?? item.channel?.id,
    enable: item.enable ?? item.enabled ?? false,
    activeTime: item.activeTime ?? item.lastActiveTime ?? item.lastChangeTime ?? null,
    deviceStatus: item.deviceStatus ?? item.status ?? item.state ?? (item.enable ? 'Running' : 'Stopped'),
    variableCount: item.variableCount ?? item.variablesCount ?? item.variableTotal ?? 0,
    pluginName: item.pluginName ?? item.fullPluginName ?? item.plugin?.name ?? ''
  }
}

function parsePaged(result: any) {
  // 支持多种分页返回格式
  if (!result) return { records: [], total: 0, current: pagination.currentPage, size: pagination.pageSize }
  const data = result.data?.data || result.data || result
  const records = data.records || data.list || []
  return {
    records,
    total: data.total ?? data.Total ?? records.length,
    current: data.current ?? data.pageIndex ?? pagination.currentPage,
    size: data.size ?? data.pageSize ?? pagination.pageSize
  }
}

async function loadData() {
  loading.value = true
  try {
    const res = await getDeviceList({ pageIndex: pagination.currentPage, pageSize: pagination.pageSize })
    const paged = parsePaged(res)
    const mapped = (paged.records as any[]).map(normalizeRow)
    // 仅显示采集设备
    tableData.value = mapped.filter(r => isCollectPluginName(getRowPluginName(r)))
    pagination.total = paged.total
    pagination.currentPage = paged.current
    pagination.pageSize = paged.size
  } catch (err) {
    console.error('加载设备列表失败:', err)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载通道列表
async function loadChannelList() {
  try {
    const res = await getChannelList()
    const paged = parsePaged(res)
    channelList.value = paged.records || []
  } catch (err) {
    console.error('加载通道列表失败:', err)
    ElMessage.error('加载通道列表失败')
  }
}

// 加载设备列表（用于冗余设备选择）
async function loadDeviceList() {
  try {
    const res = await getDeviceList({ pageIndex: 1, pageSize: 1000 })
    const paged = parsePaged(res)
    deviceList.value = (paged.records as any[]).map(normalizeRow)
  } catch (err) {
    console.error('加载设备列表失败:', err)
  }
}

// 通道选择变化时触发
async function onChannelChanged(channelId: number) {
  if (!channelId) {
    showPluginTab.value = false
    isCollectChannel.value = false
    return
  }
  
  try {
    // 1. 获取选中通道的插件名称
    const selectedChannel = channelList.value.find(c => c.id === channelId)
    if (!selectedChannel || !selectedChannel.pluginName) {
      showPluginTab.value = false
      isCollectChannel.value = false
      return
    }
    
    // 2. 判断是否为采集通道
    // 根据插件名称判断是否为采集通道，而不是根据pluginType
    // 常见的采集插件包括：Modbus、OpcUa、S7、Ethernet等
    const collectPluginKeywords = ['Modbus', 'OpcUa', 'S7', 'Ethernet', 'Collect', 'Driver']
    const isCollect = collectPluginKeywords.some(keyword => 
      selectedChannel.pluginName?.includes(keyword)
    )
    isCollectChannel.value = isCollect
    
    console.log('插件名称:', selectedChannel.pluginName)
    console.log('pluginType:', selectedChannel.pluginType)
    console.log('判断为采集通道:', isCollect)
    
    // 3. 显示插件信息标签页
    showPluginTab.value = true
    
    // 4. 如果有已保存的设备属性，加载到表单中
    const deviceProps = formData.devicePropertys || {}
    if (Object.keys(deviceProps).length > 0) {
      loadSavedProperties()
    }
    
  } catch (error) {
    console.error('加载通道信息失败:', error)
    ElMessage.error('加载通道信息失败')
    showPluginTab.value = false
    isCollectChannel.value = false
  }
}

// 加载已保存的属性
function loadSavedProperties() {
  if (isCollectChannel.value) {
    // 采集通道：加载采集通道配置
    const savedProps = formData.devicePropertys ?? {}
    Object.keys(collectChannelForm).forEach(key => {
      if (Object.prototype.hasOwnProperty.call(savedProps, key)) {
        (collectChannelForm as any)[key] = savedProps[key]
      }
    })
  } else {
    // 业务通道：加载业务通道配置
    const savedProps = formData.devicePropertys ?? {}
    Object.keys(businessChannelForm).forEach(key => {
      if (Object.prototype.hasOwnProperty.call(savedProps, key)) {
        (businessChannelForm as any)[key] = savedProps[key]
      }
    })
  }
}

// 收集插件属性
function collectPluginProperties(): Record<string, any> {
  const properties: Record<string, any> = {}
  
  if (isCollectChannel.value) {
    // 采集通道：收集采集通道配置
    Object.keys(collectChannelForm).forEach(key => {
      const value = (collectChannelForm as any)[key]
      if (value !== null && value !== undefined && value !== '') {
        properties[key] = value
      }
    })
  } else {
    // 业务通道：收集业务通道配置
    Object.keys(businessChannelForm).forEach(key => {
      const value = (businessChannelForm as any)[key]
      if (value !== null && value !== undefined && value !== '') {
        properties[key] = value
      }
    })
  }
  
  console.log('收集到的插件属性:', properties)
  return properties
}



function handleSizeChange(size: number) {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadData()
}

function handleCurrentChange(page: number) {
  pagination.currentPage = page
  loadData()
}

// 表格勾选变化
function handleSelectionChange(selection: DeviceRow[]) {
  selectedIds.value = selection
    .map(s => Number(s.id))
    .filter(id => !!id)
}

function formatDateTime(value: any): string {
  if (!value) return '-'
  try {
    let date: Date
    if (value instanceof Date) date = value
    else if (typeof value === 'number') date = new Date(value > 1e12 ? value : value * 1000)
    else date = new Date(value)
    if (Number.isNaN(date.getTime())) return '-'
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
  } catch {
    return '-'
  }
}

function getStatusText(row: DeviceRow): string {
  const status = row.deviceStatus
  if (typeof status === 'string') {
    const s = status.toLowerCase()
    if (s.includes('run')) return '运行中'
    if (s.includes('pause')) return '已暂停'
    if (s.includes('error') || s.includes('fault')) return '异常'
    if (s.includes('stop')) return '已停止'
    return status
  }
  if (typeof status === 'number') {
    // 0:停止 1:运行 2:暂停 3:异常
    return ({ 0: '已停止', 1: '运行中', 2: '已暂停', 3: '异常' } as any)[status] ?? (row.enable ? '运行中' : '已停止')
  }
  return row.enable ? '运行中' : '已停止'
}

function getStatusTag(row: DeviceRow): 'success' | 'warning' | 'danger' | 'info' {
  const text = getStatusText(row)
  if (text === '运行中') return 'success'
  if (text === '已暂停') return 'warning'
  if (text === '异常') return 'danger'
  if (text === '已停止') return 'info'
  return 'info'
}

// 显示新增弹窗
function showAddDialog() {
  resetForm()
  dialogVisible.value = true
  loadChannelList()
  loadDeviceList()
}

// 行内编辑
function handleEdit(row: DeviceRow) {
  resetForm()
  // 将行数据回填到表单
  formData.id = Number(row.id || 0)
  formData.name = row.name || ''
  formData.description = row.description || ''
  formData.channelId = Number(row.channelId || 0)
  formData.enable = Boolean(row.enable)
  formData.logLevel = (row.logLevel as any) || 'Info'
  // 设备属性与冗余字段（如果后端返回了）
  const props = (row as any).devicePropertys || {}
  formData.devicePropertys = { ...props }
  formData.redundantEnable = Boolean((row as any).redundantEnable ?? false)
  formData.redundantDeviceId = (row as any).redundantDeviceId ?? null
  formData.redundantSwitchType = (row as any).redundantSwitchType ?? 0
  formData.redundantScanIntervalTime = (row as any).redundantScanIntervalTime ?? 30000
  formData.redundantScript = (row as any).redundantScript ?? ''
  formData.remark1 = (row as any).remark1 ?? ''
  formData.remark2 = (row as any).remark2 ?? ''
  formData.remark3 = (row as any).remark3 ?? ''
  formData.remark4 = (row as any).remark4 ?? ''
  formData.remark5 = (row as any).remark5 ?? ''

  // 根据选择的通道类型，切换到对应tab
  onChannelChanged(Number(formData.channelId))
  activeTab.value = 'device'

  dialogVisible.value = true
  loadChannelList()
  loadDeviceList()
}

// 删除
function handleDelete(row: DeviceRow) {
  const id = Number(row.id)
  if (!id) {
    ElMessage.error('无效的设备ID')
    return
  }
  ElMessageBox.confirm('确定删除该设备吗？此操作不可恢复', '提示', {
    confirmButtonText: '删除',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await deleteDevice([id], true)
      ElMessage.success('删除成功')
      loadData()
    } catch (e) {
      console.error('删除失败:', e)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 批量删除
function handleBatchDelete() {
  if (selectedIds.value.length === 0) return
  ElMessageBox.confirm(`确定删除选中的 ${selectedIds.value.length} 条设备吗？`, '提示', {
    confirmButtonText: '删除',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await deleteDevice(selectedIds.value, true)
      ElMessage.success('批量删除成功')
      // 清空选择
      selectedIds.value = []
      ;(tableRef.value as any)?.clearSelection?.()
      loadData()
    } catch (e) {
      console.error('批量删除失败:', e)
      ElMessage.error('批量删除失败')
    }
  }).catch(() => {})
}

// 关闭弹窗
function closeDialog() {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
function resetForm() {
  Object.assign(formData, {
    id: 0,
    name: '',
    description: '',
    channelId: 0,
    intervalTime: '1000',
    enable: true,
    logLevel: 'Info',
    devicePropertys: {},
    redundantEnable: false,
    redundantDeviceId: null,
    redundantSwitchType: 0,
    redundantScanIntervalTime: 30000,
    redundantScript: '',
    remark1: '',
    remark2: '',
    remark3: '',
    remark4: '',
    remark5: ''
  })
  
  // 重置插件相关
  showPluginTab.value = false
  isCollectChannel.value = false // 重置采集通道状态
  
  // 重置采集通道表单
  collectChannelForm.protocolType = 'ModbusTcp'
  collectChannelForm.defaultStation = 1
  collectChannelForm.dtuRegisterPacket = ''
  collectChannelForm.maxPacketLength = 100
  collectChannelForm.timeout = 3000
  collectChannelForm.sendDelay = 0
  collectChannelForm.stringReverse = false
  collectChannelForm.parseRule = 'ABCD'
  collectChannelForm.retryCount = 3
  
  // 重置业务通道表单
  businessChannelForm.ip = '127.0.0.1'
  businessChannelForm.port = 1883
  businessChannelForm.protocolVersion = 'V311'
  businessChannelForm.qos = 'AtMostOnce'
  businessChannelForm.username = 'admin'
  businessChannelForm.password = '111111'
  businessChannelForm.connectionId = 'ThingsGatewayId'
  businessChannelForm.connectionTimeout = 3000
  businessChannelForm.tls = false
  businessChannelForm.websocket = false
  businessChannelForm.websocketUrl = 'ws://127.0.0.1:8083/mqtt'
  businessChannelForm.allowRpcWrite = false
  businessChannelForm.rpcWriteTopic = ''
  businessChannelForm.rpcRequestDataTopic = ''
  businessChannelForm.variableTopic = 'ThingsGateway/Variable'
  businessChannelForm.deviceTopic = ''
  businessChannelForm.alarmTopic = ''
  businessChannelForm.uploadMode = 'Change'
  businessChannelForm.timedUploadInterval = 1000
  businessChannelForm.uploadItemsPerPage = 2000
  businessChannelForm.variableListUpload = true
  businessChannelForm.deviceStatusListUpload = true
  businessChannelForm.alarmListUpload = true
  businessChannelForm.selectAllVariables = false
  businessChannelForm.jsonIndentFormatting = true
  businessChannelForm.enableCache = false
  businessChannelForm.groupUpload = false
  businessChannelForm.filterOfflineVariables = false
  businessChannelForm.detailedLog = false
  businessChannelForm.maxCacheFileLength = 1024
  businessChannelForm.maxMemoryQueueSize = 100000
  businessChannelForm.caFile = ''
  businessChannelForm.clientCertificate = ''
  businessChannelForm.clientKeyFile = ''
  
  formRef.value?.clearValidate()
  collectFormRef.value?.clearValidate() // 重置采集通道表单验证
  businessFormRef.value?.clearValidate() // 重置业务通道表单验证
}

// 保存设备
async function handleSave() {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 如果是采集通道，验证采集通道表单
    if (isCollectChannel.value && collectFormRef.value) {
      await collectFormRef.value.validate()
    }
    
    // 如果是业务通道，验证业务通道表单
    if (!isCollectChannel.value && businessFormRef.value) {
      await businessFormRef.value.validate()
    }
    
    saveLoading.value = true
    
    // 0. 额外前置校验
    if (/\./.test(formData.name)) {
      ElMessage.error('名称不能包含点号(.)')
      return
    }
    if (!formData.channelId || Number(formData.channelId) <= 0) {
      ElMessage.error('请选择有效的通道')
      return
    }

    // 1. 收集插件属性配置
    if (isCollectChannel.value) {
      // 采集通道：收集采集通道配置
      formData.devicePropertys = { ...collectChannelForm }
      console.log('保存采集通道配置:', formData.devicePropertys)
    } else {
      // 业务通道：收集业务通道配置
      formData.devicePropertys = collectPluginProperties()
      console.log('保存业务通道配置:', formData.devicePropertys)
    }
    
    const deviceData: DevicePayloadItem = {
      ...formData,
      channelId: Number(formData.channelId),
      intervalTime: String(formData.intervalTime),
      redundantScanIntervalTime: Number(formData.redundantScanIntervalTime),
      redundantDeviceId: formData.redundantDeviceId == null
        ? null
        : Number(formData.redundantDeviceId)
    }
    
    // type: 0 新增/更新; 1 删除（此处统一走保存接口）
    const type: ItemChangedType = formData.id && formData.id !== 0 ? 'Update' : 'Add'
    await batchSaveDevice([deviceData], type, true)
    ElMessage.success(type === 'Update' ? '设备更新成功' : '设备添加成功')
    closeDialog()
    loadData() // 重新加载列表
  } catch (err) {
    console.error('保存设备失败:', err)
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

// 添加通道（预留功能）
function handleAddChannel() {
  ElMessage.info('通道管理功能待实现')
}

// 新增：判断当前通道是否为采集通道
const isCollectChannel = ref(false)







// 新增：处理文件选择
function handleFileSelect(field: string) {
  ElMessage.info(`文件选择功能待实现，字段: ${field}`)
}

// 根据插件名称判断是否为采集通道
function isCollectPluginName(name?: string): boolean {
  if (!name) return false
  const collectPluginKeywords = ['Modbus', 'OpcUa', 'S7', 'Ethernet', 'Collect', 'Driver']
  return collectPluginKeywords.some(keyword => name.includes(keyword))
}

function getRowPluginName(row: DeviceRow): string {
  if (row.pluginName) return row.pluginName
  const channel = channelList.value.find(c => c.id === Number(row.channelId))
  return channel?.pluginName || ''
}

onMounted(async () => {
  await loadChannelList()
  await loadData()
})
</script>

<style scoped>
.device-container {
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-header h2 {
  margin: 0;
  color: #303133;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 弹窗样式 */
.device-tabs {
  margin-top: -20px;
}

.device-form {
  padding: 20px 0;
}

.form-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 20px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.channel-select {
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-channel-btn {
  flex-shrink: 0;
}

.channel-desc {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}

/* 插件表单样式 */
.plugin-form {
  padding: 20px 0;
}

.plugin-properties-form {
  margin-top: 20px;
}

.plugin-empty {
  padding: 40px 0;
  text-align: center;
}

.debug-info {
  background-color: #f5f7fa;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 6px;
  font-size: 13px;
  color: #606266;
  border-left: 4px solid #409eff;
}

.debug-info p {
  margin: 5px 0;
  line-height: 1.4;
}

.debug-info strong {
  color: #303133;
}

/* 手动测试按钮样式 */
.debug-info .el-button {
  margin-top: 10px;
  margin-right: 10px;
}

.debug-info .el-button:last-child {
  margin-right: 0;
}

.plugin-properties-list .el-descriptions {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 4px;
}

:deep(.el-input .el-input-group__append) {
  background-color: #f5f7fa;
  border-left: 1px solid #dcdfe6;
}

:deep(.el-input-number .el-input__wrapper) {
  border-radius: 4px;
}

/* 新增：采集通道配置样式 */
.collect-channel-config {
  padding: 20px 0;
}

.collect-channel-form {
  margin-top: 20px;
}

.collect-channel-form .el-form-item {
  margin-bottom: 20px;
}

.collect-channel-form .el-form-item__label {
  font-weight: 500;
}

/* 新增：文件选择弹窗样式 */
.file-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-select-dialog .el-dialog__body {
  padding: 20px;
}

/* 新增：插件属性配置样式 */
.plugin-properties-form {
  margin-top: 20px;
}

.plugin-properties-form .el-form-item {
  margin-bottom: 20px;
}

.plugin-properties-form .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.plugin-properties-form .el-input,
.plugin-properties-form .el-input-number,
.plugin-properties-form .el-select {
  width: 100%;
}

.plugin-properties-form .el-switch {
  margin-top: 4px;
}

.plugin-properties-form .el-switch .el-switch__label {
  font-size: 12px;
}

/* 采集通道配置样式 */
.collect-channel-config {
  padding: 20px 0;
}

.collect-channel-form {
  margin-top: 20px;
}

.collect-channel-form .el-form-item {
  margin-bottom: 20px;
}

.collect-channel-form .el-form-item__label {
  font-weight: 500;
}

/* 业务通道配置样式 */
.business-channel-config {
  padding: 20px 0;
}

.business-channel-form {
  margin-top: 20px;
}

.business-channel-form .el-form-item {
  margin-bottom: 20px;
}

.business-channel-form .el-form-item__label {
  font-weight: 500;
}

.subsection-title {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  margin: 0 0 15px 0;
  padding-bottom: 5px;
  border-bottom: 1px solid #e4e7ed;
}
</style>