<template>
 <div>
   <!--条件渲染 -->
   <div class="home-container" v-if="!iframeUrl">
     <!-- 默认主页：引导 + 统计 + 架构图 -->
     <div class="main-layout">
       <div class="left-column">
         <!-- 物联网引导 -->
         <div class="guide-card">
           <div class="card-header">
             <i class="iconfont icon-guide"></i>
             <span>物联网引导</span>
           </div>
           <div class="card-content">
             <div class="step-container-vertical">
               <div class="step-item-vertical" @click="goToCreateProduct">
                 <div class="step-icon">
                   <i class="el-icon-goods"></i>
                 </div>
                 <div class="step-info">
                   <div class="step-title">STEP1</div>
                   <div class="step-content">创建产品</div>
                 </div>
                 <div class="step-number">1</div>
               </div>
               <div class="step-item-vertical" @click="goToCreateDevice">
                 <div class="step-icon">
                   <i class="el-icon-cpu"></i>
                 </div>
                 <div class="step-info">
                   <div class="step-title">STEP2</div>
                   <div class="step-content">创建设备</div>
                 </div>
                 <div class="step-number">2</div>
               </div>
               <div class="step-item-vertical" @click="goToAlertConfig">
                 <div class="step-icon">
                   <i class="el-icon-warning-outline"></i>
                 </div>
                 <div class="step-info">
                   <div class="step-title">STEP3</div>
                   <div class="step-content">告警配置</div>
                 </div>
                 <div class="step-number">3</div>
               </div>
             </div>
           </div>
         </div>

         <!-- 平台特点和系统概览 -->
         <div class="features-overview-card">
           <div class="card-content features-overview-content">
             <div class="features-overview-layout">
               <!-- 平台特点区域 -->
               <div class="platform-features">
                 <div class="sidebar-title">平台特点</div>
                 <div class="sidebar-content">
                   <div class="feature-item">
                     <i class="el-icon-connection"></i>
                     <span>多协议接入</span>
                   </div>
                   <div class="feature-item">
                     <i class="el-icon-data-line"></i>
                     <span>实时数据分析</span>
                   </div>
                   <div class="feature-item">
                     <i class="el-icon-monitor"></i>
                     <span>可视化监控</span>
                   </div>
                   <div class="feature-item">
                     <i class="el-icon-warning-outline"></i>
                     <span>智能告警</span>
                   </div>
                   <div class="feature-item">
                     <i class="el-icon-s-operation"></i>
                     <span>远程控制</span>
                   </div>
                 </div>
               </div>

               <!-- 系统概览区域 -->
               <div class="system-overview-section">
                 <div class="sidebar-title">
                   设备统计详情
                   <div class="refresh-info">
                     <span v-if="dashboardData.lastUpdateTime" class="update-time">更新: {{ dashboardData.lastUpdateTime }}</span>
                     <i class="el-icon-refresh" :class="{'is-loading': dashboardData.isLoading}" @click="getDashboardData"></i>
                   </div>
                 </div>
                 <div class="system-overview">
                   <div class="overview-item">
                     <div class="overview-label">未激活</div>
                     <div class="overview-value">
                       <span class="value-number">{{ dashboardData.unactivatedDeviceCount }}</span>
                       <span class="value-unit">台</span>
                     </div>
                   </div>
                   <div class="overview-item">
                     <div class="overview-label">禁用</div>
                     <div class="overview-value">
                       <span class="value-number">{{ dashboardData.disabledDeviceCount }}</span>
                       <span class="value-unit">台</span>
                     </div>
                   </div>
                   <div class="overview-item">
                     <div class="overview-label">在线</div>
                     <div class="overview-value">
                       <span class="value-number">{{ dashboardData.onlineDeviceCount }}</span>
                       <span class="value-unit">台</span>
                     </div>
                   </div>
                   <div class="overview-item">
                     <div class="overview-label">离线</div>
                     <div class="overview-value">
                       <span class="value-number">{{ dashboardData.offlineDeviceCount }}</span>
                       <span class="value-unit">台</span>
                     </div>
                   </div>
                 </div>
               </div>
             </div>
           </div>
         </div>
       </div>

       <div class="right-column">
         <div class="stats-row">
           <!-- 设备统计 -->
           <div class="stats-card">
             <div class="card-header">
               <i class="iconfont icon-statistics"></i>
               <span>设备统计</span>
             </div>
             <div class="card-content">
               <div class="stats-container">
                 <div class="stats-item">
                   <div class="stats-title">产品数量</div>
                   <div class="stats-content">
                     <span class="stats-number">{{ productCount }}</span>
                     <div class="stats-icon product-icon"></div>
                   </div>
                 </div>
                 <div class="stats-item">
                   <div class="stats-title">设备数量</div>
                   <div class="stats-content">
                     <span class="stats-number">{{ deviceCount }}</span>
                     <div class="stats-icon device-icon"></div>
                   </div>
                 </div>
               </div>
             </div>
           </div>

           <!-- 基础统计 -->
           <div class="basic-stats-card">
             <div class="card-header">
               <i class="iconfont icon-stats"></i>
               <span>基础统计</span>
             </div>
             <div class="card-content">
               <div class="stats-charts-container">
                 <div class="stats-chart-item">
                   <div class="stats-chart-title">CPU使用率</div>
                   <div class="stats-chart-content">
                     <div id="cpuChart" class="stats-chart"></div>
                     <div class="stats-chart-value">{{ dashboardData.cpuUsage }}%</div>
                   </div>
                 </div>
                 <div class="stats-chart-item">
                   <div class="stats-chart-title">内存使用率</div>
                   <div class="stats-chart-content">
                     <div id="memoryChart" class="stats-chart"></div>
                     <div class="stats-chart-value">{{ dashboardData.memoryUsage }}%</div>
                   </div>
                 </div>
               </div>
             </div>
           </div>
         </div>
         <!-- 中部布局 -->
         <div class="middle-layout">
           <!-- 平台架构图 -->
           <div class="architecture-card">
             <div class="card-header">
               <span>海南三区二号站示例图</span>
               <i class="iconfont icon-architecture"></i>
             </div>
             <div class="card-content architecture-content">
               <div class="architecture-diagram">
                 <img src="@/assets/images/background.png" alt="平台架构图" class="architecture-img" />
               </div>
             </div>
           </div>
         </div>
       </div>
     </div>
   </div>

   <!-- 当有 iframeUrl 时，全屏显示 iframe -->
   <div class="iframe-full-container" v-else>
     <iframe :src="iframeUrl" class="embedded-frame" ></iframe>
   </div>
 </div>
</template>

<script setup lang="ts" name="home">
import { ref, onMounted, reactive, onUnmounted, nextTick } from 'vue'
import { listProduct } from '/@/api/iot/product'
import { listDeviceShort } from '/@/api/iot/device'
import { listAlertLog } from '/@/api/iot/alertLog'
import { getServer } from '/@/api/monitor/server'
import { getMockSystemResources } from '/@/api/iot/dashboard'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts/core'
import { PieChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import {useUserInfo} from "@/stores/userInfo";
import {Session} from "@/utils/storage";
const iframeUrl = ref<string | null>(null); // 存储处理后的 URL
import { getConfigKey } from '/@/api/system/config';
const loading = ref<boolean>(true);

/**
 * 从字符串中提取所有 ${xxx} 中的键名
 */
function extractPlaceholderKeys(template: string): string[] {
  const regex = /\$\{([^}]+)\}/g;
  const keys: string[] = [];
  let match;
  while ((match = regex.exec(template)) !== null) {
    keys.push(match[1]); // match[1] 是 ${} 中的内容
  }
  return [...new Set(keys)]; // 去重
}

/**
 * 渲染模板：自动提取占位符，调用接口替换
 */
const renderTemplate = async (template: string): Promise<string> => {
  let url = template;
  const keys = extractPlaceholderKeys(template);

  // 并行请求所有配置项（性能更好）
  const configPromises = keys.map(key => {
    return getConfigKey(key).then(res => ({
      key,
      value: res.data?.msg?.trim() ?? ''
    })).catch(err => {
      console.warn(`获取配置失败: ${key}`, err);
      return { key, value: '' }; // 失败返回空
    });
  });

  const results = await Promise.all(configPromises);
  // 构建映射表
  const configMap = results.reduce((map, { key, value }) => {
    map[key] = value;
    return map;
  }, {} as Record<string, string>);

  // 特殊处理：token 可能来自 Session
  const token = Session.get('token') || '';
  if (template.includes('${token}')) {
    configMap['token'] = token;
  }

  // 替换所有 ${key}
  url = template;
  for (const [key, value] of Object.entries(configMap)) {
    const regex = new RegExp(`\\$\\{${key}\\}`, 'g');
    url = url.replace(regex, encodeURIComponent(value));
  }

  return url;
};
const userStore = useUserInfo();
/**
 * 加载页面
 */
const loadChart = async () => {
  loading.value = true;
  try {

    const template = userStore.userInfos.component;

    if (template) {
      // 渲染模板
      let url = await renderTemplate(template);
      url = decodeURIComponent(url);
      // 验证协议
      const httpRegex = /^https?:\/\//;
      if (!httpRegex.test(url)) {
        console.error('无效的 URL 协议（必须是 http 或 https）:', url);
        return;
      }

      // 更新 iframe
      iframeUrl.value = url;
    }
  } catch (error) {
    console.error('加载首页失败:', error);
  } finally {
    loading.value = false;
  }
};

// 注册必要的组件
echarts.use([TitleComponent, TooltipComponent, LegendComponent, PieChart, CanvasRenderer])

// 定义系统资源响应类型
interface SystemResourceResponse {
  code: number;
  msg: string;
  data: {
    cpu: {
      usage: number;
      cores?: number;
      model?: string;
    };
    mem: {
      usage: number;
      total?: number;
      used?: number;
      free?: number;
    };
    sys?: {
      computerName?: string;
      osName?: string;
      osArch?: string;
      uptime?: string;
    };
  };
}

// 路由实例
const router = useRouter()

// 定义产品和设备数量的响应式变量
const productCount = ref(0)
const deviceCount = ref(0)

// 定义仪表盘数据
const dashboardData = reactive({
  unactivatedDeviceCount: 0, // 未激活
  disabledDeviceCount: 0,    // 禁用
  onlineDeviceCount: 0,      // 在线
  offlineDeviceCount: 0,     // 离线
  todayAlertCount: 0,
  cpuUsage: 0,     // CPU使用率
  memoryUsage: 0,  // 内存使用率
  isLoading: false, // 加载状态标志
  lastUpdateTime: '' // 最后更新时间
})

// 保存图表实例的引用
let cpuChart: echarts.ECharts | null = null
let memoryChart: echarts.ECharts | null = null

// 初始化饼图
const initCharts = () => {
  // 确保DOM已经渲染
  nextTick(() => {
    // 初始化CPU使用率图表
    if (document.getElementById('cpuChart')) {
      cpuChart = echarts.init(document.getElementById('cpuChart') as HTMLElement)
      updateCpuChart()
    }
    
    // 初始化内存使用率图表
    if (document.getElementById('memoryChart')) {
      memoryChart = echarts.init(document.getElementById('memoryChart') as HTMLElement)
      updateMemoryChart()
    }
  })
}

// 更新CPU使用率图表
const updateCpuChart = () => {
  if (!cpuChart) return
  
  const option = {
    series: [
      {
        type: 'pie',
        radius: ['60%', '80%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          }
        },
        data: [
          { 
            value: dashboardData.cpuUsage, 
            name: '已用', 
            itemStyle: { 
              color: dashboardData.cpuUsage > 90 ? '#f56c6c' : 
                     dashboardData.cpuUsage > 80 ? '#e6a23c' : '#3370ff' 
            } 
          },
          { value: 100 - dashboardData.cpuUsage, name: '空闲', itemStyle: { color: '#ebeef5' } }
        ]
      }
    ]
  }
  
  cpuChart.setOption(option)
}

// 更新内存使用率图表
const updateMemoryChart = () => {
  if (!memoryChart) return
  
  const option = {
    series: [
      {
        type: 'pie',
        radius: ['60%', '80%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          }
        },
        data: [
          { 
            value: dashboardData.memoryUsage, 
            name: '已用', 
            itemStyle: { 
              color: dashboardData.memoryUsage > 90 ? '#f56c6c' : 
                     dashboardData.memoryUsage > 80 ? '#e6a23c' : '#3370ff' 
            } 
          },
          { value: 100 - dashboardData.memoryUsage, name: '空闲', itemStyle: { color: '#ebeef5' } }
        ]
      }
    ]
  }
  
  memoryChart.setOption(option)
}

// 跳转到创建产品页面
const goToCreateProduct = () => {
  router.push('/iot/product')
}

// 跳转到创建设备页面
const goToCreateDevice = () => {
  router.push('/iot/device')
}

// 跳转到告警配置页面
const goToAlertConfig = () => {
  router.push('/ruleengine/alert')
}

// 获取产品总数
const getProductCount = async () => {
  try {
    const response = await listProduct({ pageNum: 1, pageSize: 1 })
    if (response && response.data && response.data.total !== undefined) {
      productCount.value = response.data.total
    } else if (response && response.total !== undefined) {
      productCount.value = response.total
    } else {
      console.error('产品API响应格式不符合预期:', response)
    }
  } catch (error) {
    console.error('获取产品数量失败:', error)
  }
}

// 获取设备总数
const getDeviceCount = async () => {
  try {
    const response = await listDeviceShort({ pageNum: 1, pageSize: 1 })
    if (response && response.data && response.data.total !== undefined) {
      deviceCount.value = response.data.total
    } else if (response && response.total !== undefined) {
      deviceCount.value = response.total
    } else {
      console.error('设备API响应格式不符合预期:', response)
    }
  } catch (error) {
    console.error('获取设备数量失败:', error)
  }
}

// 获取仪表盘概览数据
const getDashboardData = async () => {
  // 设置加载状态
  dashboardData.isLoading = true;
  
  try {
    // 1. 获取未激活数量
    try {
      const res = await listDeviceShort({ pageNum: 1, pageSize: 1, status: 1 })
      dashboardData.unactivatedDeviceCount = res?.data?.total ?? res?.total ?? 0
    } catch (err) {
      console.error('获取未激活设备数量失败:', err)
      dashboardData.unactivatedDeviceCount = 0
    }
    // 2. 获取禁用数量
    try {
      const res = await listDeviceShort({ pageNum: 1, pageSize: 1, status: 2 })
      dashboardData.disabledDeviceCount = res?.data?.total ?? res?.total ?? 0
    } catch (err) {
      console.error('获取禁用设备数量失败:', err)
      dashboardData.disabledDeviceCount = 0
    }
    // 3. 获取在线数量
    try {
      const res = await listDeviceShort({ pageNum: 1, pageSize: 1, status: 3 })
      dashboardData.onlineDeviceCount = res?.data?.total ?? res?.total ?? 0
    } catch (err) {
      console.error('获取在线设备数量失败:', err)
      dashboardData.onlineDeviceCount = 0
    }
    // 4. 获取离线数量
    try {
      const res = await listDeviceShort({ pageNum: 1, pageSize: 1, status: 4 })
      dashboardData.offlineDeviceCount = res?.data?.total ?? res?.total ?? 0
    } catch (err) {
      console.error('获取离线设备数量失败:', err)
      dashboardData.offlineDeviceCount = 0
    }
    // 5. 获取今日告警数量
    try {
      const today = new Date()
      const startTime = today.setHours(0, 0, 0, 0)
      const alertResponse = await listAlertLog({ 
        pageNum: 1, 
        pageSize: 1,
        params: {
          beginTime: new Date(startTime).toISOString().split('T')[0]
        }
      })
      if (alertResponse && (alertResponse.total !== undefined || (alertResponse.data && alertResponse.data.total !== undefined))) {
        dashboardData.todayAlertCount = alertResponse.total || (alertResponse.data && alertResponse.data.total) || 0
      }
    } catch (err) {
      console.error('获取今日告警数量失败:', err)
      dashboardData.todayAlertCount = Math.floor(Math.random() * 20)
    }
    // 6. 获取服务器CPU和内存使用情况
    try {
      let serverRes = await getServer()
      let hasValidData = false
      // 适配真实接口结构
      if (serverRes && serverRes.data) {
        // CPU使用率
        if (serverRes.data.cpu && typeof serverRes.data.cpu.usage !== 'undefined') {
          dashboardData.cpuUsage = parseFloat(serverRes.data.cpu.usage)
          hasValidData = true
        }
        // 内存使用率
        if (serverRes.data.mem && typeof serverRes.data.mem.usage !== 'undefined') {
          dashboardData.memoryUsage = parseFloat(serverRes.data.mem.usage)
          hasValidData = true
        }
      }
      if (!hasValidData) {
        try {
          const mockRes: SystemResourceResponse = await getMockSystemResources()
          if (mockRes && mockRes.code === 200 && mockRes.data) {
            dashboardData.cpuUsage = mockRes.data.cpu?.usage || Math.floor(Math.random() * 60) + 20
            dashboardData.memoryUsage = mockRes.data.mem?.usage || Math.floor(Math.random() * 50) + 30
          } else {
            dashboardData.cpuUsage = Math.floor(Math.random() * 60) + 20
            dashboardData.memoryUsage = Math.floor(Math.random() * 50) + 30
          }
        } catch (mockErr) {
          console.error('模拟数据获取失败:', mockErr)
          dashboardData.cpuUsage = Math.floor(Math.random() * 60) + 20
          dashboardData.memoryUsage = Math.floor(Math.random() * 50) + 30
        }
      }
    } catch (err) {
      console.error('获取服务器资源信息失败:', err)
      try {
        const mockRes: SystemResourceResponse = await getMockSystemResources()
        if (mockRes && mockRes.data && mockRes.data.cpu && mockRes.data.mem) {
          dashboardData.cpuUsage = mockRes.data.cpu.usage
          dashboardData.memoryUsage = mockRes.data.mem.usage
        } else {
          dashboardData.cpuUsage = Math.floor(Math.random() * 60) + 20
          dashboardData.memoryUsage = Math.floor(Math.random() * 50) + 30
        }
      } catch (mockErr) {
        console.error('模拟数据获取失败:', mockErr)
        dashboardData.cpuUsage = Math.floor(Math.random() * 60) + 20
        dashboardData.memoryUsage = Math.floor(Math.random() * 50) + 30
      }
    }
    // 更新最后更新时间
    const now = new Date();
    dashboardData.lastUpdateTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
    // 更新图表
    updateCpuChart()
    updateMemoryChart()
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
    dashboardData.unactivatedDeviceCount = 0
    dashboardData.disabledDeviceCount = 0
    dashboardData.onlineDeviceCount = 0
    dashboardData.offlineDeviceCount = 0
    dashboardData.todayAlertCount = Math.floor(Math.random() * 20)
    dashboardData.cpuUsage = Math.floor(Math.random() * 60) + 20
    dashboardData.memoryUsage = Math.floor(Math.random() * 50) + 30
    const now = new Date();
    dashboardData.lastUpdateTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
  } finally {
    dashboardData.isLoading = false;
  }
}

// 定义定时器
let dashboardTimer: number | null = null;
const updateInterval = 30000; // 每30秒更新一次

// 页面加载时获取数据并设置定时器
onMounted(() => {
  getProductCount()
  getDeviceCount()
  getDashboardData()
  //初始化自定义首页
  loadChart();
  // 初始化图表
  initCharts()
  
  // 设置定时器定期刷新数据
  dashboardTimer = window.setInterval(() => {
    getDashboardData()
  }, updateInterval)
  
  // 监听窗口大小变化，重新调整图表大小
  window.addEventListener('resize', handleResize)
})

// 处理窗口大小变化
const handleResize = () => {
  if (cpuChart) cpuChart.resize()
  if (memoryChart) memoryChart.resize()
}

// 页面卸载时清除定时器和事件监听
onUnmounted(() => {
  if (dashboardTimer !== null) {
    clearInterval(dashboardTimer)
    dashboardTimer = null
  }
  
  // 移除事件监听
  window.removeEventListener('resize', handleResize)
  
  // 销毁图表实例
  if (cpuChart) {
    cpuChart.dispose()
    cpuChart = null
  }
  
  if (memoryChart) {
    memoryChart.dispose()
    memoryChart = null
  }
})
</script>
<style  lang="scss"  scoped>
/* 可添加样式 */
.iframe-full-container {
  position: relative;
  width: 100%;
  height: 100dvh;
}
.embedded-frame {
  position: absolute;
  width: 100%;
  height: 100%;
}
</style>

<style lang="scss" scoped>
.home-container {
  padding: 12px;
  background-color: #f5f7fa;
  min-height: 100vh; /* 让整体高度铺满视口 */
  display: flex;
  flex-direction: column;
}

.main-layout {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
  flex: 1; /* 让主布局自动填充剩余空间 */
  min-height: 0;
}

.left-column {
  flex: 1.2;
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;
}

.right-column {
  flex: 2.2;
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;
}

.middle-layout {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;
}

.architecture-card {
  flex: 1 1 0%;
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;
}

.architecture-content {
  padding: 0;
  flex: 1 1 0%;
  height: 100%;
  min-height: 0;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.architecture-diagram {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.architecture-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.guide-card, .stats-card, .architecture-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 0;
  overflow: hidden;
}

.guide-card {
  margin-bottom: 0; /* 移除底部间距 */
}

.stats-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 0;
  overflow: hidden;
  flex: 1;
  margin-bottom: 0;
}

.basic-stats-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 0;
  overflow: hidden;
  flex: 1;
  margin-bottom: 15px;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  font-weight: bold;
  font-size: 16px; /* 调整为更适中的字体大小 */
  letter-spacing: 0.5px; /* 稍微减小字间距 */
  color: #333; /* 更深的文字颜色 */
  border-bottom: 1px solid #ebeef5;
  position: relative;

  i {
    margin-right: 10px;
    color: #3370ff;
    font-size: 18px; /* 调整图标大小 */
  }
  
  span {
    position: relative;
    padding-bottom: 4px;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -6px;
      left: 0;
      width: 30px;
      height: 3px;
      background-color: #3370ff;
      border-radius: 2px;
    }
  }
  
  .header-subtitle {
    font-size: 12px;
    color: #909399;
    margin-left: 10px;
    font-weight: normal;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  /* 已移除详情链接样式 */

  .sub-title {
    font-size: 12px;
    color: #909399;
    margin-left: 10px;
    font-weight: normal;
  }
}

.card-content {
  padding: 15px;
}

.stats-card .card-content,
.basic-stats-card .card-content {
  padding: 10px;
  height: 150px; /* 固定高度使其更紧凑 */
}

/* 竖向步骤布局 */
.step-container-vertical {
  display: flex;
  flex-direction: column;
  padding: 10px 0;
}

.step-item-vertical {
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: all 0.3s;
  padding: 12px 15px;
  margin-bottom: 8px;
  border-radius: 4px;
  border-left: 4px solid #3370ff;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.step-item-vertical:hover {
  background-color: #f0f7ff;
  transform: translateX(5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.step-item-vertical:active {
  transform: translateX(2px);
  background-color: #e6f0ff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.step-icon {
  width: 36px;
  height: 36px;
  background-color: #4785f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.step-icon i {
  color: #fff;
  font-size: 20px;
}

.step-info {
  flex: 1;
}

.step-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
  position: relative;
  z-index: 1;
}

.step-content {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  position: relative;
  z-index: 1;
}

.step-number {
  position: absolute;
  right: 20px;
  font-size: 80px;
  color: rgba(240, 242, 245, 0.8);
  z-index: 0;
  font-weight: bold;
  line-height: 1;
  font-family: Arial, sans-serif;
}

.stats-container {
  display: flex;
  gap: 16px;
}

.stats-item {
  flex: 1;
  background: linear-gradient(135deg, #f6faff 60%, #e3eafd 100%);
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.06);
  padding: 24px 16px 0 16px;
  min-height: 140px;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.stats-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
  z-index: 2;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #222;
  margin-bottom: 18px;
  z-index: 2;
}

.product-icon, .device-icon {
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 90px;
  height: 90px;
  opacity: 0.18;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center bottom;
  z-index: 1;
  border-radius: 0;
  margin: 0;
}

.features-overview-layout {
  display: flex;
  width: 100%;
  height: 100%;
}

.features-overview-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 0;
  overflow: hidden;
  margin-top: -1px; /* 使其与物联网引导卡片无缝连接 */
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.features-overview-content {
  padding: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex: 1;
}

.features-overview-layout {
  display: flex;
  width: 100%;
}

/* 系统概览区域样式 */
.system-overview-section {
  width: 50%;
  padding: 15px 20px; /* 减小上下内边距 */
  background-color: #f9fafc;
  position: relative;
  z-index: 10;
  border-left: 1px solid #ebeef5;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.platform-features {
  width: 50%;
  padding: 15px 20px; /* 减小上下内边距 */
  border-right: 1px solid #ebeef5;
  background-color: #f9fafc;
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.sidebar-item {
  margin-bottom: 30px; /* 增加间距 */
  position: relative; /* 添加相对定位 */
  z-index: 5; /* 确保正确的层叠顺序 */
}

.sidebar-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .refresh-info {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: normal;
    color: #909399;
    
    .update-time {
      margin-right: 8px;
    }
    
    i {
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s;
      color: #3370ff;
      
      &:hover {
        color: #1a56ff;
      }
      
      &.is-loading {
        animation: rotating 2s linear infinite;
      }
    }
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  i {
    font-size: 20px;
    color: #3370ff;
    margin-right: 10px;
  }

  span {
    font-size: 14px;
    color: #606266;
  }
}

.system-overview {
  background-color: #fff;
  border-radius: 4px;
  padding: 15px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 50px;
  z-index: 10; /* 确保在前面显示 */
  position: relative; /* 添加相对定位 */
}

.overview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px dashed #ebeef5;
  
  &:last-child {
    border-bottom: none;
  }
}

.overview-label {
  font-size: 14px;
  color: #606266;
}

.overview-value {
  display: flex;
  align-items: baseline;
}

.value-number {
  font-size: 20px;
  font-weight: bold;
  color: #3370ff;
  margin-right: 4px;
}

.value-unit {
  font-size: 12px;
  color: #909399;
}

/* 基础统计区域样式 */
.stats-charts-container {
  display: flex;
  justify-content: space-around;
  padding: 0;
}

.stats-chart-item {
  flex: 1;
  max-width: 200px;
  text-align: center;
  padding: 10px;
}

.stats-chart-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
  text-align: center;
}

.stats-chart-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.stats-chart {
  width: 85px;
  height: 85px;
}

.stats-chart-value {
  position: absolute;
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.stats-row {
  display: flex;
  gap: 12px;
  width: 100%;
  align-items: stretch; /* 保证高度一致 */
  margin-bottom: 0;
}

.stats-card,
.basic-stats-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: 100%;
}

/* URL参数测试区域样式 */
.url-test-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.test-info {
  font-size: 13px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10px;
}

.test-info p {
  margin: 5px 0;
}

.test-info strong {
  color: #333;
}

.test-links {
  font-size: 14px;
}

.test-links a {
  color: #409eff;
  text-decoration: none;
  margin: 0 5px;
}

.test-links a:hover {
  text-decoration: underline;
}
</style>
