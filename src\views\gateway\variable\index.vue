<template>
  <div class="variable-container">
    <!-- 页面标题和操作按钮 -->
    <div class="table-header">
      <h2>变量列表</h2>
      <div>
        <el-button type="info" @click="loadData" style="margin-right: 10px;">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="primary" @click="showAddDialog">
          <el-icon><Plus /></el-icon>
          新增变量
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="变量名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入变量名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="设备名称">
          <el-input
            v-model="searchForm.deviceName"
            placeholder="请输入设备名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="变量地址">
          <el-input
            v-model="searchForm.registerAddress"
            placeholder="请输入变量地址"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 实时更新控制 -->
    <div style="margin-bottom: 10px; padding: 10px; background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 4px;">
      <div style="display: flex; justify-content: space-between; align-items: center;">
                 <!-- <div>
           <p><strong>实时数据状态:</strong></p>
           <p>数据条数: {{ tableData.length }} / {{ pagination.total }}</p>
           <p>在线变量: {{ onlineCount }} / 离线变量: {{ offlineCount }}</p>
           <p>加载状态: {{ loading ? '加载中' : '已完成' }}</p>
           <p>分页信息: 第{{ pagination.currentPage }}页，每页{{ pagination.pageSize }}条</p>
           <p v-if="pagination.total > 1000" style="color: #e6a23c; font-size: 12px;">
             <el-icon><Warning /></el-icon>
             大数据量模式：建议使用搜索功能或调整页面大小以提升性能
           </p>
         </div> -->
        <div style="display: flex; align-items: center; gap: 10px;">
          <el-switch
            v-model="isAutoRefresh"
            active-text="自动刷新"
            inactive-text="手动刷新"
            @change="toggleAutoRefresh"
          />
          <el-select
            v-model="refreshInterval"
            style="width: 120px"
            @change="startAutoRefresh"
            :disabled="!isAutoRefresh"
          >
            <el-option label="1秒" :value="1000" />
            <el-option label="3秒" :value="3000" />
            <el-option label="5秒" :value="5000" />
            <el-option label="10秒" :value="10000" />
            <el-option label="30秒" :value="30000" />
            <el-option label="1分钟" :value="60000" />
            <el-option label="2分钟" :value="120000" />
            <el-option label="3分钟" :value="180000" />
            <el-option label="4分钟" :value="240000" />
            <el-option label="5分钟" :value="300000" />
            <el-option label="30分钟" :value="1800000" />
          </el-select>
          <el-tag v-if="isAutoRefresh" type="success" size="small">
            <el-icon><CircleCheck /></el-icon>
            自动刷新中
          </el-tag>
          <el-tag v-else type="info" size="small">
            <el-icon><CircleClose /></el-icon>
            手动模式
          </el-tag>
        </div>
      </div>
    </div>

         <!-- 变量表格 -->
     <el-table
       :data="tableData"
       stripe
       border
       style="width: 100%"
       v-loading="loading"
       @selection-change="handleSelectionChange"
       :height="600"
       :max-height="600"
     >
      <!-- 选择框 -->
      <el-table-column
        type="selection"
        width="55"
        align="center"
      />

      <!-- 行号 -->
      <el-table-column
        type="index"
        label="行号"
        width="80"
        align="center"
      />

      <!-- 名称 -->
      <el-table-column
        prop="name"
        label="名称"
        min-width="160"
        show-overflow-tooltip
        sortable
      />



      <!-- 描述 -->
      <el-table-column
        prop="description"
        label="描述"
        min-width="180"
        show-overflow-tooltip
        sortable
      >
        <template #default="{ row }">
          {{ row.description || '-' }}
        </template>
      </el-table-column>

      <!-- 设备名称 -->
      <el-table-column
        prop="deviceName"
        label="设备名称"
        min-width="140"
        show-overflow-tooltip
        sortable
      />

      <!-- 在线状态 -->
      <el-table-column
        prop="isOnline"
        label="在线"
        width="100"
        align="center"
        sortable
      >
        <template #default="{ row }">
          <el-tag :type="row.isOnline ? 'success' : 'danger'">
            {{ row.isOnline ? '在线' : '离线' }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 保护类型 -->
      <!-- <el-table-column
        prop="protectType"
        label="保护类型"
        width="100"
        align="center"
        sortable
      >
        <template #default="{ row }">
          <el-tag :type="row.protectType === ProtectTypeEnum.ReadOnly ? 'info' : row.protectType === ProtectTypeEnum.ReadWrite ? 'success' : 'warning'">
            {{ mapProtectType(row.protectType) }}
          </el-tag>
        </template>
      </el-table-column> -->

      <!-- 动态类型 -->
      <el-table-column
        prop="dataType"
        label="数据类型"
        min-width="120"
        show-overflow-tooltip
        sortable
      >
        <template #default="{ row }">
          {{ mapDataType(row.dataType) }}
        </template>
      </el-table-column>

             <!-- 实时值 -->
       <el-table-column
         prop="value"
         label="实时值"
         min-width="120"
         align="center"
         sortable
       >
         <template #default="{ row }">
           <span 
             :key="`${row.id}-${row.value}-${row.updateTimestamp || 0}`"
             class="real-time-value"
             :class="{ 'value-updated': row.updateTimestamp && Date.now() - row.updateTimestamp < 1000 }"
           >
             {{ formatValue(row.value, row.dataType) }}
           </span>
         </template>
       </el-table-column>

      <!-- 变量地址 -->
      <el-table-column
        prop="registerAddress"
        label="变量地址"
        min-width="120"
        align="center"
        sortable
      />

      <!-- 操作 -->
      <el-table-column
        label="操作"
        width="200"
        align="center"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleEdit(row)"
          >
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(row)"
          >
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
                 :page-sizes="[50, 100, 200, 500]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑变量对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="变量名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入变量名称"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="设备" prop="deviceId">
          <el-select
            v-model="formData.deviceId"
            placeholder="请选择设备"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="device in deviceOptions"
              :key="device.value"
              :label="device.label"
              :value="device.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入变量描述"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="单位" prop="unit">
          <el-input
            v-model="formData.unit"
            placeholder="请输入单位"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="变量地址" prop="registerAddress">
          <el-input
            v-model="formData.registerAddress"
            placeholder="请输入变量地址"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="数据类型" prop="dataType">
          <el-select
            v-model="formData.dataType"
            placeholder="请选择数据类型"
            style="width: 100%"
          >
            <el-option label="对象" :value="DataTypeEnum.Object" />
            <el-option label="字符串" :value="DataTypeEnum.String" />
            <el-option label="布尔值" :value="DataTypeEnum.Boolean" />
            <el-option label="字节" :value="DataTypeEnum.Byte" />
            <el-option label="16位整数" :value="DataTypeEnum.Int16" />
            <el-option label="16位无符号整数" :value="DataTypeEnum.UInt16" />
            <el-option label="32位整数" :value="DataTypeEnum.Int32" />
            <el-option label="32位无符号整数" :value="DataTypeEnum.UInt32" />
            <el-option label="64位整数" :value="DataTypeEnum.Int64" />
            <el-option label="64位无符号整数" :value="DataTypeEnum.UInt64" />
            <el-option label="单精度浮点数" :value="DataTypeEnum.Single" />
            <el-option label="双精度浮点数" :value="DataTypeEnum.Double" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="保护类型" prop="protectType">
          <el-select
            v-model="formData.protectType"
            placeholder="请选择保护类型"
            style="width: 100%"
          >
            <el-option label="只读" :value="ProtectTypeEnum.ReadOnly" />
            <el-option label="读写" :value="ProtectTypeEnum.ReadWrite" />
            <el-option label="只写" :value="ProtectTypeEnum.WriteOnly" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="是否启用" prop="enable">
          <el-switch
            v-model="formData.enable"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        
        <el-form-item label="允许RPC写入" prop="rpcWriteEnable">
          <el-switch
            v-model="formData.rpcWriteEnable"
            active-text="允许"
            inactive-text="禁止"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, Edit, Delete, CircleCheck, CircleClose, Warning } from '@element-plus/icons-vue'
import {
  getVariableList,
  createVariable,
  updateVariable,
  deleteVariable,
  getDeviceListForVariable,
  type VariablePageInput,
  type VariableRuntime,
  type VariableInput,
  DataTypeEnum,
  ProtectTypeEnum
} from '@/api/gateway/variable'

// 类型定义
interface SearchForm {
  name: string
  deviceName: string
  registerAddress: string
}

interface Pagination {
  currentPage: number
  pageSize: number
  total: number
}

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const submitLoading = ref(false)
const formRef = ref()
const selectedRows = ref<VariableRuntime[]>([])
// 轮询模式，不使用WebSocket

const tableData = ref<VariableRuntime[]>([])
const deviceOptions = ref<{ label: string; value: string }[]>([])
// 统计基于当前页计算
const onlineCount = computed(() => tableData.value.filter(v => v.isOnline).length)
const offlineCount = computed(() => tableData.value.length - onlineCount.value)

const searchForm = reactive<SearchForm>({
  name: '',
  deviceName: '',
  registerAddress: ''
})

const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 50, // 增加默认页面大小，减少分页次数
  total: 0
})

const formData = reactive<VariableInput>({
  deviceId: 0,
  name: '',
  description: '',
  unit: '',
  intervalTime: '',
  registerAddress: '',
  arrayLength: undefined,
  otherMethod: '',
  enable: true,
  protectType: ProtectTypeEnum.ReadWrite,
  dataType: DataTypeEnum.Int32,
  readExpressions: '',
  writeExpressions: '',
  rpcWriteEnable: true,
  initValue: undefined,
  saveValue: false,
  alarmDelay: 0,
  boolOpenAlarmEnable: false,
  boolCloseAlarmEnable: false,
  hAlarmEnable: false,
  hAlarmCode: undefined,
  hhAlarmEnable: false,
  hhAlarmCode: undefined,
  lAlarmEnable: false,
  lAlarmCode: undefined,
  llAlarmEnable: false,
  llAlarmCode: undefined,
  customAlarmEnable: false,
  customAlarmCode: '',
  hAlarmText: '',
  hhAlarmText: '',
  lAlarmText: '',
  llAlarmText: '',
  customAlarmText: '',
  hRestrainExpressions: '',
  hhRestrainExpressions: '',
  lRestrainExpressions: '',
  llRestrainExpressions: '',
  customRestrainExpressions: '',
  remark1: '',
  remark2: '',
  remark3: '',
  remark4: '',
  remark5: ''
})

const formRules = {
  name: [
    { required: true, message: '请输入变量名称', trigger: 'blur' }
  ],
  deviceId: [
    { required: true, message: '请选择设备', trigger: 'change' }
  ],
  registerAddress: [
    { required: true, message: '请输入变量地址', trigger: 'blur' }
  ],
  dataType: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ],
  protectType: [
    { required: true, message: '请选择保护类型', trigger: 'change' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  return formData.id ? '编辑变量' : '新增变量'
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params: VariablePageInput = {
      pageIndex: pagination.currentPage,
      pageSize: pagination.pageSize,
      name: searchForm.name || undefined,
      deviceName: searchForm.deviceName || undefined,
      registerAddress: searchForm.registerAddress || undefined,
      businessDeviceId: undefined
    }
    
    console.log('请求参数:', params)
    const response = await getVariableList(params)
    console.log('API响应:', response)
    
    // 检查响应结构 - request.ts 返回的是整个 response 对象
    if (response.data && response.data.code === 200 && response.data.data) {
      const data = response.data.data
      console.log('处理后的数据:', data)
      
      if (data.records && Array.isArray(data.records)) {
        console.log('变量列表数据:', data.records)
        tableData.value = data.records
        pagination.total = data.total || data.records.length
        pagination.currentPage = data.current || pagination.currentPage
        pagination.pageSize = data.size || pagination.pageSize
      } else {
        console.log('没有找到数据记录')
        tableData.value = []
        pagination.total = 0
      }
    } else if (response.data && response.data.code !== 200) {
      console.log('API返回错误:', response.data)
      tableData.value = []
      pagination.total = 0
    } else {
      console.log('API响应格式不正确:', response)
      tableData.value = []
      pagination.total = 0
    }
    
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.currentPage = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    deviceName: '',
    registerAddress: ''
  })
  pagination.currentPage = 1
  loadData()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const handleSelectionChange = (rows: VariableRuntime[]) => {
  selectedRows.value = rows
}

// WebSocket相关逻辑已移除，使用轮询刷新实时值

const showAddDialog = () => {
  Object.assign(formData, {
    id: undefined,
    deviceId: 0,
    name: '',
    description: '',
    unit: '',
    intervalTime: '',
    registerAddress: '',
    arrayLength: undefined,
    otherMethod: '',
    enable: true,
    protectType: ProtectTypeEnum.ReadWrite,
    dataType: DataTypeEnum.Int32,
    readExpressions: '',
    writeExpressions: '',
    rpcWriteEnable: true,
    initValue: undefined,
    saveValue: false,
    alarmDelay: 0,
    boolOpenAlarmEnable: false,
    boolCloseAlarmEnable: false,
    hAlarmEnable: false,
    hAlarmCode: undefined,
    hhAlarmEnable: false,
    hhAlarmCode: undefined,
    lAlarmEnable: false,
    lAlarmCode: undefined,
    llAlarmEnable: false,
    llAlarmCode: undefined,
    customAlarmEnable: false,
    customAlarmCode: '',
    hAlarmText: '',
    hhAlarmText: '',
    lAlarmText: '',
    llAlarmText: '',
    customAlarmText: '',
    hRestrainExpressions: '',
    hhRestrainExpressions: '',
    lRestrainExpressions: '',
    llRestrainExpressions: '',
    customRestrainExpressions: '',
    remark1: '',
    remark2: '',
    remark3: '',
    remark4: '',
    remark5: ''
  })
  dialogVisible.value = true
}

const handleEdit = (row: VariableRuntime) => {
  Object.assign(formData, {
    id: row.id,
    deviceId: row.deviceId,
    name: row.name,
    description: row.description || '',
    unit: row.unit || '',
    intervalTime: row.intervalTime || '',
    registerAddress: row.registerAddress || '',
    arrayLength: row.arrayLength,
    otherMethod: row.otherMethod || '',
    enable: row.enable,
    protectType: row.protectType,
    dataType: row.dataType,
    readExpressions: row.readExpressions || '',
    writeExpressions: row.writeExpressions || '',
    rpcWriteEnable: row.rpcWriteEnable,
    initValue: undefined,
    saveValue: false,
    alarmDelay: row.alarmDelay || 0,
    boolOpenAlarmEnable: false,
    boolCloseAlarmEnable: false,
    hAlarmEnable: row.hAlarmEnable || false,
    hAlarmCode: row.hAlarmCode,
    hhAlarmEnable: row.hhAlarmEnable || false,
    hhAlarmCode: row.hhAlarmCode,
    lAlarmEnable: row.lAlarmEnable || false,
    lAlarmCode: row.lAlarmCode,
    llAlarmEnable: row.llAlarmEnable || false,
    llAlarmCode: row.llAlarmCode,
    customAlarmEnable: false,
    customAlarmCode: '',
    hAlarmText: '',
    hhAlarmText: '',
    lAlarmText: '',
    llAlarmText: '',
    customAlarmText: '',
    hRestrainExpressions: '',
    hhRestrainExpressions: '',
    lRestrainExpressions: '',
    llRestrainExpressions: '',
    customRestrainExpressions: '',
    remark1: '',
    remark2: '',
    remark3: '',
    remark4: '',
    remark5: ''
  })
  dialogVisible.value = true
}

const handleDelete = async (row: VariableRuntime) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除变量"${row.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteVariable([row.id])
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (formData.id) {
      // 更新变量
      await updateVariable(formData)
      ElMessage.success('更新成功')
    } else {
      // 新增变量
      await createVariable(formData)
      ElMessage.success('添加成功')
    }
    
    dialogVisible.value = false
    loadData()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    submitLoading.value = false
  }
}

// 数据类型映射函数
const mapDataType = (dataType: number): string => {
  switch (dataType) {
    case DataTypeEnum.Object: return 'Object'
    case DataTypeEnum.String: return 'String'
    case DataTypeEnum.Boolean: return 'Boolean'
    case DataTypeEnum.Byte: return 'Byte'
    case DataTypeEnum.Int16: return 'Int16'
    case DataTypeEnum.UInt16: return 'UInt16'
    case DataTypeEnum.Int32: return 'Int32'
    case DataTypeEnum.UInt32: return 'UInt32'
    case DataTypeEnum.Int64: return 'Int64'
    case DataTypeEnum.UInt64: return 'UInt64'
    case DataTypeEnum.Single: return 'Single'
    case DataTypeEnum.Double: return 'Double'
    default: return 'Int32'
  }
}

// 保护类型映射函数
const mapProtectType = (protectType: number): string => {
  switch (protectType) {
    case ProtectTypeEnum.ReadOnly: return 'ReadOnly'
    case ProtectTypeEnum.ReadWrite: return 'ReadWrite'
    case ProtectTypeEnum.WriteOnly: return 'WriteOnly'
    default: return 'ReadWrite'
  }
}

const formatValue = (value: any, dataType: number) => {
  if (value === null || value === undefined) return '-'
  
  const dataTypeStr = mapDataType(dataType)
  
  switch (dataTypeStr) {
    case 'Boolean':
      return value ? 'true' : 'false'
    case 'String':
      return String(value)
    case 'Int16':
    case 'UInt16':
    case 'Int32':
    case 'UInt32':
    case 'Int64':
    case 'UInt64':
      return parseInt(value)
    case 'Single':
    case 'Double':
      return parseFloat(value).toFixed(2)
    default:
      return value
  }
}

// 加载设备列表
const loadDevices = async () => {
  try {
    const response = await getDeviceListForVariable()
    console.log('设备列表响应:', response)
    
    if (response.data && response.data.code === 200 && response.data.data) {
      const data = response.data.data
      if (data.records && Array.isArray(data.records)) {
        deviceOptions.value = data.records.map((item: any) => ({
          label: item.name || item.label || item,
          value: item.id || item.value || item
        }))
        console.log('设备选项:', deviceOptions.value)
      }
    } else if (response.data && response.data.code !== 200) {
      console.log('设备列表API返回错误:', response.data)
    } else {
      console.log('设备列表响应格式不正确:', response)
    }
  } catch (error) {
    console.error('加载设备列表失败:', error)
  }
}

// 在响应式数据部分添加
const refreshTimer = ref<NodeJS.Timeout | null>(null)
const isAutoRefresh = ref(true)
const refreshInterval = ref(5000) // 默认5秒，可在下拉中切换 1秒~30分钟
const searchTimer = ref<NodeJS.Timeout | null>(null) // 搜索防抖定时器

// 在方法部分添加
// 开始自动刷新
const startAutoRefresh = () => {
  if (!isAutoRefresh.value) return
  
  stopAutoRefresh() // 先停止之前的定时器
  
  refreshTimer.value = setInterval(() => {
    if (!document.hidden) { // 页面可见时才刷新
      refreshRealTimeValues()
    }
  }, refreshInterval.value)
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 只刷新实时值（优化性能）——改为调用当前页列表接口
const refreshRealTimeValues = async () => {
  if (tableData.value.length === 0) return
  try {
    const params: VariablePageInput = {
      pageIndex: pagination.currentPage,
      pageSize: pagination.pageSize,
      name: searchForm.name || undefined,
      deviceName: searchForm.deviceName || undefined,
      registerAddress: searchForm.registerAddress || undefined,
      businessDeviceId: undefined
    }
    const response = await getVariableList(params)
    if (response.data && response.data.code === 200 && response.data.data) {
      const data = response.data.data
      if (data.records && Array.isArray(data.records)) {
        const latestMap = new Map<number, any>()
        data.records.forEach((item: any) => latestMap.set(item.id, item))
        tableData.value = tableData.value.map(oldItem => {
          const fresh = latestMap.get(oldItem.id)
          if (!fresh) return oldItem
          const oldValue = oldItem.value
          const merged: any = {
            ...oldItem,
            value: fresh.value,
            isOnline: fresh.isOnline,
            changeTime: fresh.changeTime,
            collectTime: fresh.collectTime
          }
        if (oldValue !== fresh.value) merged.updateTimestamp = Date.now()
          return merged
        })
      }
    }
  } catch (error) {
    console.error('刷新实时值失败，使用回退方案:', error)
    try { await loadData() } catch (fallbackError) { console.error('回退方案也失败:', fallbackError) }
  }
}

// 切换自动刷新
const toggleAutoRefresh = () => {
  isAutoRefresh.value = !isAutoRefresh.value
  if (isAutoRefresh.value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

// 搜索防抖监听
watch(() => [searchForm.name, searchForm.deviceName, searchForm.registerAddress], () => {
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }
  
  searchTimer.value = setTimeout(() => {
    pagination.currentPage = 1
    loadData()
  }, 500) // 500ms防抖
}, { deep: true })

// 修改生命周期钩子
onMounted(() => {
  loadData()
  loadDevices()
  startAutoRefresh() // 启动自动刷新
})

onUnmounted(() => {
  stopAutoRefresh() // 清理定时器
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }
})
</script>

<style scoped>
.variable-container {
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-header h2 {
  margin: 0;
  color: #303133;
}

.search-section {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

.real-time-value {
  font-weight: bold;
  color: #409eff;
  transition: all 0.3s ease;
}

.value-updated {
  animation: valueUpdate 0.5s ease-in-out;
}

@keyframes valueUpdate {
  0% {
    background-color: transparent;
    transform: scale(1);
  }
  50% {
    background-color: #e6f7ff;
    transform: scale(1.05);
  }
  100% {
    background-color: transparent;
    transform: scale(1);
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-table .el-table__row:hover) {
  background-color: #f5f7fa;
}

:deep(.el-table .el-table__row--striped) {
  background-color: #fafafa;
}

:deep(.el-table .el-table__row--striped:hover) {
  background-color: #f5f7fa;
}
</style>