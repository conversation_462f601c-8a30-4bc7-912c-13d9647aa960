#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例API模块
演示如何使用Java权限系统的简单接口
"""
from datetime import datetime
from typing import List, Dict, Any

from fastapi import APIRouter, Request, HTTPException
from pydantic import BaseModel

router = APIRouter()


class DemoItem(BaseModel):
    """示例数据模型"""
    id: int
    name: str
    description: str
    created_at: datetime
    created_by: str


class CreateDemoRequest(BaseModel):
    """创建示例数据请求"""
    name: str
    description: str


class UpdateDemoRequest(BaseModel):
    """更新示例数据请求"""
    name: str = None
    description: str = None


# 模拟数据存储
demo_data: List[DemoItem] = [
    DemoItem(
        id=1,
        name="示例项目1",
        description="这是第一个示例项目",
        created_at=datetime.now(),
        created_by="admin"
    ),
    DemoItem(
        id=2,
        name="示例项目2", 
        description="这是第二个示例项目",
        created_at=datetime.now(),
        created_by="user1"
    )
]


@router.get("/list", summary="获取示例列表")
async def get_demo_list(request: Request) -> Dict[str, Any]:
    """
    获取示例数据列表
    权限要求: demo:list
    """
    # 获取当前用户信息（由权限中间件设置）
    user_id = getattr(request.state, 'user_id', 'anonymous')
    username = getattr(request.state, 'username', 'Anonymous')
    
    return {
        "code": 200,
        "message": "获取成功",
        "data": {
            "items": [item.dict() for item in demo_data],
            "total": len(demo_data),
            "current_user": {
                "user_id": user_id,
                "username": username
            }
        }
    }


@router.post("/create", summary="创建示例项目")
async def create_demo_item(request: Request, item_data: CreateDemoRequest) -> Dict[str, Any]:
    """
    创建新的示例项目
    权限要求: demo:create
    """
    # 获取当前用户信息
    user_id = getattr(request.state, 'user_id', 'anonymous')
    username = getattr(request.state, 'username', 'Anonymous')
    
    # 生成新ID
    new_id = max([item.id for item in demo_data], default=0) + 1
    
    # 创建新项目
    new_item = DemoItem(
        id=new_id,
        name=item_data.name,
        description=item_data.description,
        created_at=datetime.now(),
        created_by=username
    )
    
    demo_data.append(new_item)
    
    return {
        "code": 200,
        "message": "创建成功",
        "data": {
            "item": new_item.dict(),
            "created_by": {
                "user_id": user_id,
                "username": username
            }
        }
    }


@router.get("/{item_id}", summary="获取示例项目详情")
async def get_demo_item(request: Request, item_id: int) -> Dict[str, Any]:
    """
    获取指定示例项目的详情
    权限要求: demo:detail
    """
    # 获取当前用户信息
    user_id = getattr(request.state, 'user_id', 'anonymous')
    username = getattr(request.state, 'username', 'Anonymous')
    
    # 查找项目
    item = next((item for item in demo_data if item.id == item_id), None)
    if not item:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    return {
        "code": 200,
        "message": "获取成功",
        "data": {
            "item": item.dict(),
            "current_user": {
                "user_id": user_id,
                "username": username
            }
        }
    }


@router.put("/{item_id}", summary="更新示例项目")
async def update_demo_item(request: Request, item_id: int, update_data: UpdateDemoRequest) -> Dict[str, Any]:
    """
    更新指定示例项目
    权限要求: demo:update
    """
    # 获取当前用户信息
    user_id = getattr(request.state, 'user_id', 'anonymous')
    username = getattr(request.state, 'username', 'Anonymous')
    
    # 查找项目
    item = next((item for item in demo_data if item.id == item_id), None)
    if not item:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    # 更新项目
    if update_data.name is not None:
        item.name = update_data.name
    if update_data.description is not None:
        item.description = update_data.description
    
    return {
        "code": 200,
        "message": "更新成功",
        "data": {
            "item": item.dict(),
            "updated_by": {
                "user_id": user_id,
                "username": username
            }
        }
    }


@router.delete("/{item_id}", summary="删除示例项目")
async def delete_demo_item(request: Request, item_id: int) -> Dict[str, Any]:
    """
    删除指定示例项目
    权限要求: demo:delete
    """
    # 获取当前用户信息
    user_id = getattr(request.state, 'user_id', 'anonymous')
    username = getattr(request.state, 'username', 'Anonymous')
    
    # 查找并删除项目
    global demo_data
    item = next((item for item in demo_data if item.id == item_id), None)
    if not item:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    demo_data = [item for item in demo_data if item.id != item_id]
    
    return {
        "code": 200,
        "message": "删除成功",
        "data": {
            "deleted_item_id": item_id,
            "deleted_by": {
                "user_id": user_id,
                "username": username
            }
        }
    }


@router.get("/stats/summary", summary="获取统计信息")
async def get_demo_stats(request: Request) -> Dict[str, Any]:
    """
    获取示例项目统计信息
    权限要求: demo:stats
    """
    # 获取当前用户信息
    user_id = getattr(request.state, 'user_id', 'anonymous')
    username = getattr(request.state, 'username', 'Anonymous')
    
    # 统计信息
    total_count = len(demo_data)
    user_items = [item for item in demo_data if item.created_by == username]
    user_count = len(user_items)
    
    return {
        "code": 200,
        "message": "获取成功",
        "data": {
            "stats": {
                "total_items": total_count,
                "user_items": user_count,
                "other_items": total_count - user_count
            },
            "current_user": {
                "user_id": user_id,
                "username": username
            }
        }
    }
