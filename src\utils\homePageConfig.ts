import { useUserInfo } from '/@/stores/userInfo';
import { useDefIndexStore } from '/@/stores/defIndexStore';


export enum PageEnum {
  ROOT_PATH = '/',
  BASE_LOGIN = '/login',
  BASE_HOME = '/home',
  ERROR_404 = '/404',
  ERROR_401 = '/401',
}


export interface HomePageConfig {
  url: string;
  component: string;
  priority: number;
  source: 'role' | 'menu' | 'system';
  roleCode?: string;
  description?: string;
}

/**
 * 获取用户首页配置
 * 按优先级顺序：角色配置首页 > 菜单配置默认首页 > 系统默认首页
 */
export async function getUserHomePageConfig(): Promise<HomePageConfig> {
  try {
    const userStore = useUserInfo();

    if (!userStore.userInfos?.roles?.length) {
      await userStore.setUserInfos();
    }

    const userRoles = userStore.userInfos?.roles || [];

    // 1. 优先级最高：角色配置首页
    const roleConfig = await getRoleHomePageConfig(userRoles);
    if (roleConfig) {
      return roleConfig;
    }

    // 2. 中等优先级：菜单配置默认首页
    const menuConfig = await getMenuDefaultHomePageConfig();
    if (menuConfig) {
      return menuConfig;
    }

    // 3. 最低优先级：系统默认首页
    const systemConfig = getSystemDefaultConfig();
    return systemConfig;

  } catch (error) {
    return getSystemDefaultConfig();
  }
}

/**
 * 获取角色首页配置 (最高优先级)
 * 从 sys_role_index 表中查询用户角色对应的首页配置
 * 按 priority 字段升序排序，取第一个启用的配置
 */
async function getRoleHomePageConfig(userRoles: string[]): Promise<HomePageConfig | null> {
  try {
    if (!userRoles.length) {
      return null;
    }

    // 动态导入API避免循环依赖
    const { queryIndexByCode } = await import('/@/api/system/role');

    // 按角色优先级查询首页配置
    for (const roleCode of userRoles) {
      try {
        const res = await queryIndexByCode({ roleCode });
        const response = res.data;
        if (response && response.data && response.data.url && response.data.component) {
          const config = {
            url: response.data.url,
            component: response.data.component,
            priority: 1, // 最高优先级
            source: 'role' as const,
            roleCode: roleCode,
            description: `角色 ${roleCode} 的专属首页`
          };

          return config;
        }
      } catch (error) {
        continue; // 继续查询下一个角色
      }
    }
    return null;
  } catch (error) {
    return null;
  }
}

/**
 * 获取菜单配置默认首页 (中等优先级)
 * 从菜单管理中查询被设置为默认首页的菜单项
 */
async function getMenuDefaultHomePageConfig(): Promise<HomePageConfig | null> {
  try {
    const defIndexStore = useDefIndexStore();
    // 查询菜单配置的默认首页
    await defIndexStore.query();
    if (defIndexStore.url && defIndexStore.component) {
      const config = {
        url: defIndexStore.url,
        component: defIndexStore.component,
        priority: 2, // 中等优先级
        source: 'menu' as const,
        description: '菜单管理中配置的默认首页'
      };

      return config;
    }
    return null;
  } catch (error) {
    return null;
  }
}

/**
 * 获取系统默认配置 (最低优先级)
 * 当以上都没有配置时使用
 */
function getSystemDefaultConfig(): HomePageConfig {
  return {
    url: PageEnum.BASE_HOME,
    component: '/views/home/<USER>',
    priority: 3, // 最低优先级
    source: 'system',
    description: '系统硬编码默认首页'
  };
}