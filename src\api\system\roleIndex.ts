import request from '/@/utils/request';

// 定义角色首页配置的数据结构
export interface RoleIndex {
  id?: number;
  roleCode: string;
  url: string;
  component: string;
  priority?: number;
  status?: string;
  createTime?: string;
  updateTime?: string;
}

// 定义查询参数类型
interface QueryParams {
  roleCode?: string;
  status?: string;
  pageNum?: number;
  pageSize?: number;
}

// 定义返回数据的类型
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

// 查询角色首页配置列表
export function getRoleIndexList(query: QueryParams) {
  return request<ApiResponse<{ rows: RoleIndex[]; total: number }>>({
    url: '/sys/sysRoleIndex/list',
    method: 'get',
    params: query
  });
}

// 查询角色首页配置详细信息
export function getRoleIndex(id: number) {
  return request<ApiResponse<RoleIndex>>({
    url: `/sys/sysRoleIndex/${id}`,
    method: 'get'
  });
}

// 新增角色首页配置
export function addRoleIndex(data: RoleIndex) {
  return request<ApiResponse<null>>({
    url: '/sys/sysRoleIndex',
    method: 'post',
    data: data
  });
}

// 修改角色首页配置
export function updateRoleIndex(data: RoleIndex) {
  return request<ApiResponse<null>>({
    url: '/sys/sysRoleIndex',
    method: 'put',
    data: data
  });
}

// 删除角色首页配置
export function deleteRoleIndex(id: number) {
  return request<ApiResponse<null>>({
    url: `/sys/sysRoleIndex/${id}`,
    method: 'delete'
  });
}

// 批量删除角色首页配置
export function deleteRoleIndexBatch(ids: number[]) {
  return request<ApiResponse<null>>({
    url: `/sys/sysRoleIndex/${ids.join(',')}`,
    method: 'delete'
  });
}

// 根据角色编码查询首页配置
export function getRoleIndexByCode(roleCode: string) {
  return request<ApiResponse<RoleIndex>>({
    url: '/sys/sysRoleIndex/queryByCode',
    method: 'get',
    params: { roleCode }
  });
}

// 获取所有角色列表（用于下拉选择）
export function getRoleList() {
  return request<ApiResponse<{ roleCode: string; roleName: string }[]>>({
    url: '/system/role/list',
    method: 'get'
  });
}
