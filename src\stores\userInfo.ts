import { defineStore } from 'pinia';
import Cookies from 'js-cookie';
import { Session } from '/@/utils/storage';
import { loginApi, getInfo } from '/@/api/login';
import {dynamicRoutes} from '/@/router/route';
import profileImage from '../assets/profile.jpg';
/**
 * 用户信息
 * @methods setUserInfos 设置用户信息
 */
export const useUserInfo = defineStore('userInfo', {
	state: (): UserInfosState => ({
		userInfos: {
			token: Session.get('token'),
			username: '',
			photo: '',
			time: 0,
			roles: [],
			authBtnList: [],
			permissions:[],
			userId:'',
			homePath:''
		},
	}),
	actions: {
		// 登录
		async login(userInfo: any,) {
			const useinfo = await loginApi(userInfo)
			return useinfo
		},
		async setUserInfos() {
			// 检查是否有token，如果没有token说明未登录
			const token = Session.get('token') || Cookies.get('token');
			if (!token) {
				console.warn('用户未登录，无法设置用户信息');
				// 清除可能存在的过期用户信息
				Session.remove('userInfo');
				// 重定向到登录页面
				window.location.href = '/login';
				throw new Error('用户未登录');
			}
			// 存储用户信息到浏览器缓存
			if (Session.get('userInfo')) {
				this.userInfos = Session.get('userInfo');
			} else {
				const userInfos = <UserInfos>await this.getApiUserInfo();
				this.userInfos = userInfos;
			}
		},
		// 获取用户信息
		async getApiUserInfo() {
			return new Promise((resolve, reject) => {
				setTimeout(async () => {
					try {
						// 检查是否有token，如果没有token说明未登录
						const token = Session.get('token') || Cookies.get('token');
						if (!token) {
							console.warn('用户未登录，无法获取用户信息');
							reject(new Error('用户未登录'));
							return;
						}
						// 模拟数据，请求接口时，记得删除多余代码及对应依赖的引入
						// const username = Cookies.get('username');
						// // 模拟数据
						// let defaultRoles: Array<string> = [];
						// let defaultAuthBtnList: Array<string> = [];
						// // admin 页面权限标识，对应路由 meta.roles，用于控制路由的显示/隐藏
						// let adminRoles: Array<string> = ['admin'];
						// // admin 按钮权限标识
						// let adminAuthBtnList: Array<string> = ['btn.add', 'btn.del', 'btn.edit', 'btn.link'];
						// // test 页面权限标识，对应路由 meta.roles，用于控制路由的显示/隐藏
						// let testRoles: Array<string> = ['visitor'];
						// // test 按钮权限标识
						// let testAuthBtnList: Array<string> = ['btn.link'];
						// // 不同用户模拟不同的用户权限
						// if (username === 'admin') {
						// 	defaultRoles = adminRoles;
						// 	defaultAuthBtnList = adminAuthBtnList;
						// } else {
						// 	defaultRoles = testRoles;
						// 	defaultAuthBtnList = testAuthBtnList;
						// }
						// 用户信息模拟数据
						const response = await getInfo();
						// 检查API响应是否成功
						if (!response || !response.data) {
							console.error('获取用户信息API响应失败:', response);
							throw new Error('获取用户信息失败：API响应异常');
						}

						// 处理不同的响应数据结构
						let data, code;

						// 如果 response.data 直接包含用户数据
						if (response.data.user) {
							data = response.data;
							code = response.data.code || 200;
						}
						// 如果 response.data 包含嵌套的 data 字段
						else if (response.data.data) {
							data = response.data.data;
							code = response.data.code || 200;
						}
						// 如果响应结构异常
						else {
							console.error('无法识别的响应数据结构:', response.data);
							throw new Error('获取用户信息失败：响应数据结构异常');
						}

						if (code !== 200) {
							console.error('获取用户信息API返回错误码:', code, response.data);
							throw new Error(`获取用户信息失败：${response.data.msg || '未知错误'}`);
						}

						// 检查数据结构是否正确
						if (!data || !data.user) {
							console.error('用户信息数据结构错误:', data);
							console.error('完整响应数据:', response.data);
							throw new Error('获取用户信息失败：数据结构不正确');
						}

						const avatar = data.user.avatar === '' || data.user.avatar == null ? profileImage  : import.meta.env.VITE_APP_BASE_API + data.user.avatar;
						const userInfos = {
							username: data.user.userName || '',
							photo: avatar,
							time: new Date().getTime(),
							roles: data.roles || [],
							permissions: data.permissions || [],
							authBtnList: data.permissions || [],
							userId: data.user.userId || '',
							homePath: data.user.homePath || '' // 添加用户自定义首页路径
						};
						// 设置用户信息
						this.userInfos = userInfos;
						Session.set('userInfo', userInfos);
						// 异步设置首页配置（不阻塞登录流程）
						this.setUserHomePageConfig().catch(error => {
							console.error('设置首页配置失败:', error);
						});

						resolve(userInfos);
					} catch (error) {
						console.error('获取用户信息失败:', error);
						// 如果是网络错误或认证错误，清除token
						if (error instanceof Error && (error.message?.includes('401') || error.message?.includes('认证'))) {
							Session.remove('token');
							Cookies.remove('token');
						}
						reject(error);
					}
				}, 0);
			});
		},

		async setUserHomePageConfig() {
			try {
				const { getUserHomePageConfig } = await import('/@/utils/homePageConfig');
				const homeConfig = await getUserHomePageConfig();

				if (this.userInfos) {
					let  homePath = homeConfig.url.startsWith('/') ? homeConfig.url : '/' + homeConfig.url;
					let  component = homeConfig.component;
					this.userInfos.homePath = homePath;
					const dollarRegex = /\$\{([^}]+)\}/g;
					if(dollarRegex.exec(component)){
						this.userInfos.component = component;
					}

				}
			} catch (error) {
				console.error('❌ 设置用户首页配置失败:', error);
			}
		},
	},

});
