<template>
  <div class="dynamic-home-management">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>动态首页管理</span>
          <el-button type="primary" @click="openAddDialog">
            <el-icon><ele-Plus /></el-icon>
            新增配置
          </el-button>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" :inline="true">
          <el-form-item label="配置类型">
            <el-select v-model="searchForm.type" placeholder="请选择配置类型" clearable>
              <el-option label="角色配置" value="role" />
              <el-option label="菜单配置" value="menu" />
              <el-option label="系统配置" value="system" />
            </el-select>
          </el-form-item>
          <el-form-item label="角色编码">
            <el-input v-model="searchForm.roleCode" placeholder="请输入角色编码" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><ele-Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><ele-Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 配置列表 -->
      <el-table :data="configList" border style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="type" label="配置类型" width="120">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="roleCode" label="角色编码" width="120" />
        <el-table-column prop="url" label="URL路径" />
        <el-table-column prop="component" label="组件路径" />
        <el-table-column prop="priority" label="优先级" width="80" align="center" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">
              <el-icon><ele-Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="success" size="small" @click="handlePreview(scope.row)">
              <el-icon><ele-View /></el-icon>
              预览
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">
              <el-icon><ele-Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        style="margin-top: 20px; text-align: right;"
      />
    </el-card>
    
    <!-- 配置对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :before-close="handleDialogClose"
    >
      <el-form :model="configForm" :rules="configRules" ref="configFormRef" label-width="120px">
        <el-form-item label="配置类型" prop="type">
          <el-select v-model="configForm.type" placeholder="请选择配置类型">
            <el-option label="角色配置" value="role" />
            <el-option label="菜单配置" value="menu" />
          </el-select>
        </el-form-item>
        <el-form-item label="角色编码" prop="roleCode" v-if="configForm.type === 'role'">
          <el-input v-model="configForm.roleCode" placeholder="请输入角色编码" />
        </el-form-item>
        <el-form-item label="URL路径" prop="url">
          <el-input v-model="configForm.url" placeholder="例如: /system/user" />
        </el-form-item>
        <el-form-item label="组件路径" prop="component">
          <el-select v-model="configForm.component" placeholder="请选择组件" filterable>
            <el-option
              v-for="comp in availableComponents"
              :key="comp.path"
              :label="comp.name"
              :value="comp.path"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-input-number v-model="configForm.priority" :min="1" :max="100" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="configForm.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="configForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入配置描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="dynamicHomeManagement">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 搜索表单
const searchForm = reactive({
  type: '',
  roleCode: ''
});

// 配置列表
const configList = ref([
  {
    id: 1,
    type: 'role',
    roleCode: 'admin',
    url: '/system/user',
    component: '/views/system/user/index',
    priority: 1,
    status: 1,
    createTime: '2024-01-15 10:30:00',
    description: '管理员专属首页'
  },
  {
    id: 2,
    type: 'menu',
    roleCode: 'DEF_INDEX_ALL',
    url: '/dashboard',
    component: '/views/dashboard/index',
    priority: 2,
    status: 1,
    createTime: '2024-01-14 15:20:00',
    description: '系统默认首页'
  }
]);

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 对话框相关
const dialogVisible = ref(false);
const dialogTitle = ref('');
const configFormRef = ref();
const loading = ref(false);

// 配置表单
const configForm = reactive({
  id: null,
  type: '',
  roleCode: '',
  url: '',
  component: '',
  priority: 1,
  status: 1,
  description: ''
});

// 表单验证规则
const configRules = {
  type: [{ required: true, message: '请选择配置类型', trigger: 'change' }],
  url: [{ required: true, message: '请输入URL路径', trigger: 'blur' }],
  component: [{ required: true, message: '请选择组件', trigger: 'change' }],
  priority: [{ required: true, message: '请输入优先级', trigger: 'blur' }]
};

// 可用组件列表
const availableComponents = ref([
  { name: '用户管理', path: '/views/system/user/index' },
  { name: '角色管理', path: '/views/system/role/index' },
  { name: '菜单管理', path: '/views/system/menu/index' },
  { name: '设备管理', path: '/views/iot/device/index' },
  { name: '产品管理', path: '/views/iot/product/index' },
  { name: '数据可视化', path: '/views/dataVisual/index' },
  { name: '告警日志', path: '/views/iot/alertLog/index' }
]);

// 获取类型文本
const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'role': '角色配置',
    'menu': '菜单配置',
    'system': '系统配置'
  };
  return typeMap[type] || type;
};

// 获取类型标签类型
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'role': 'danger',
    'menu': 'warning',
    'system': 'info'
  };
  return typeMap[type] || 'info';
};

// 搜索
const handleSearch = () => {
  console.log('搜索条件:', searchForm);
  // 这里应该调用搜索API
  ElMessage.info('搜索功能开发中...');
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    type: '',
    roleCode: ''
  });
  handleSearch();
};

// 状态变更
const handleStatusChange = (row: any) => {
  console.log('状态变更:', row);
  ElMessage.success(`配置状态已${row.status ? '启用' : '禁用'}`);
};

// 编辑
const handleEdit = (row: any) => {
  dialogTitle.value = '编辑配置';
  Object.assign(configForm, row);
  dialogVisible.value = true;
};

// 预览
const handlePreview = (row: any) => {
  ElMessage.info(`预览配置: ${row.url}`);
  // 这里可以打开新窗口预览
  window.open(row.url, '_blank');
};

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除配置 "${row.url}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 这里应该调用删除API
    ElMessage.success('删除成功');
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

// 新增配置
const openAddDialog = () => {
  dialogTitle.value = '新增配置';
  resetConfigForm();
  dialogVisible.value = true;
};

// 重置配置表单
const resetConfigForm = () => {
  Object.assign(configForm, {
    id: null,
    type: '',
    roleCode: '',
    url: '',
    component: '',
    priority: 1,
    status: 1,
    description: ''
  });
};

// 保存配置
const handleSave = async () => {
  try {
    await configFormRef.value.validate();
    
    console.log('保存配置:', configForm);
    // 这里应该调用保存API
    
    ElMessage.success('保存成功');
    dialogVisible.value = false;
    // 刷新列表
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 关闭对话框
const handleDialogClose = () => {
  dialogVisible.value = false;
  resetConfigForm();
};

// 分页相关
const handleSizeChange = (size: number) => {
  pagination.size = size;
  // 重新加载数据
};

const handleCurrentChange = (current: number) => {
  pagination.current = current;
  // 重新加载数据
};

// 初始化
onMounted(() => {
  pagination.total = configList.value.length;
});
</script>

<style scoped>
.dynamic-home-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
