#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java系统权限查询服务
从Java系统的MySQL数据库中查询用户权限信息
"""

from typing import List, Dict, Optional
import json
import re
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from backend.database.java_db import get_java_db
from backend.database.redis import redis_client
from backend.common.log import log


class JavaPermissionService:
    """Java系统权限查询服务"""

    @staticmethod
    def _is_admin_user(user_id: int) -> bool:
        """
        判断是否为超级管理员用户 (Java系统逻辑)

        :param user_id: 用户ID
        :return: 是否为超级管理员
        """
        # Java系统逻辑: SysUser.isAdmin(userId) -> userId != null && 1L == userId
        return user_id == 1

    @staticmethod
    def _is_admin_role(role_id: int) -> bool:
        """
        判断是否为超级管理员角色 (Java系统逻辑)

        :param role_id: 角色ID
        :return: 是否为超级管理员角色
        """
        # Java系统逻辑: SysRole.isAdmin(roleId) -> roleId != null && 1L == roleId
        return role_id == 1
    
    @staticmethod
    async def get_user_permissions(user_id: int) -> Dict:
        """
        获取用户的完整权限信息

        :param user_id: 用户ID
        :return: 用户权限信息字典
        """
        try:
            async for db in get_java_db():
                # 1. 获取用户基本信息
                user_info = await JavaPermissionService._get_user_info(db, user_id)
                if not user_info:
                    return {"error": "用户不存在"}

                # 2. 检查是否为超级管理员 (Java系统逻辑: userId == 1)
                is_admin = JavaPermissionService._is_admin_user(user_id)

                # 3. 获取用户角色
                user_roles = await JavaPermissionService._get_user_roles(db, user_id)

                # 4. 获取用户权限
                if is_admin:
                    # 超级管理员直接赋予所有权限 (Java系统逻辑)
                    user_permissions = [{
                        "menu_id": 0,
                        "menu_name": "超级管理员权限",
                        "parent_id": 0,
                        "path": "*",
                        "component": "*",
                        "perms": "*:*:*",
                        "menu_type": "F",
                        "visible": "0",
                        "status": "0",
                        "order_num": 0,
                    }]
                    permission_codes = ["*:*:*"]
                    menu_paths = ["*"]
                else:
                    user_permissions = await JavaPermissionService._get_user_permissions_by_roles(db, user_roles)
                    permission_codes = [p["perms"] for p in user_permissions if p["perms"]]
                    menu_paths = [p["path"] for p in user_permissions if p["path"]]

                # 5. 组装完整权限信息
                permission_info = {
                    "user_id": user_info["user_id"],
                    "username": user_info["user_name"],
                    "nickname": user_info["nick_name"],
                    "email": user_info["email"],
                    "status": user_info["status"],
                    "dept_id": user_info["dept_id"],
                    "is_admin": is_admin,
                    "roles": user_roles,
                    "permissions": user_permissions,
                    "permission_codes": permission_codes,
                    "menu_paths": menu_paths,
                }

                log.info(f"获取用户权限成功: user_id={user_id}, is_admin={is_admin}, permissions={len(user_permissions)}")
                return permission_info
                
        except Exception as e:
            log.error(f"获取用户权限失败: user_id={user_id}, error={e}")
            return {"error": str(e)}
    
    @staticmethod
    async def _get_user_info(db: AsyncSession, user_id: int) -> Optional[Dict]:
        """获取用户基本信息"""
        try:
            query = text("""
                SELECT user_id, user_name, nick_name, email, status, dept_id, del_flag
                FROM sys_user 
                WHERE user_id = :user_id AND del_flag = '0'
            """)
            
            result = await db.execute(query, {"user_id": user_id})
            row = result.fetchone()
            
            if row:
                return {
                    "user_id": row[0],
                    "user_name": row[1],
                    "nick_name": row[2],
                    "email": row[3],
                    "status": row[4],
                    "dept_id": row[5],
                }
            return None
            
        except Exception as e:
            log.error(f"获取用户信息失败: user_id={user_id}, error={e}")
            return None
    
    @staticmethod
    async def _get_user_roles(db: AsyncSession, user_id: int) -> List[Dict]:
        """获取用户角色列表"""
        try:
            query = text("""
                SELECT r.role_id, r.role_name, r.role_key, r.status
                FROM sys_role r
                INNER JOIN sys_user_role ur ON r.role_id = ur.role_id
                WHERE ur.user_id = :user_id AND r.del_flag = '0' AND r.status = '0'
            """)
            
            result = await db.execute(query, {"user_id": user_id})
            rows = result.fetchall()
            
            roles = []
            for row in rows:
                roles.append({
                    "role_id": row[0],
                    "role_name": row[1],
                    "role_key": row[2],
                    "status": row[3],
                })
            
            return roles
            
        except Exception as e:
            log.error(f"获取用户角色失败: user_id={user_id}, error={e}")
            return []
    
    @staticmethod
    async def _get_user_permissions_by_roles(db: AsyncSession, roles: List[Dict]) -> List[Dict]:
        """根据角色获取权限列表"""
        try:
            if not roles:
                return []
            
            role_ids = [role["role_id"] for role in roles]
            role_ids_str = ",".join(map(str, role_ids))
            
            query = text(f"""
                SELECT DISTINCT m.menu_id, m.menu_name, m.parent_id, m.path,
                       m.component, m.perms, m.menu_type, m.visible, m.status, m.order_num
                FROM sys_menu m
                INNER JOIN sys_role_menu rm ON m.menu_id = rm.menu_id
                WHERE rm.role_id IN ({role_ids_str})
                  AND m.status = '0'
                ORDER BY m.order_num, m.menu_id
            """)
            
            result = await db.execute(query)
            rows = result.fetchall()
            
            permissions = []
            for row in rows:
                permissions.append({
                    "menu_id": row[0],
                    "menu_name": row[1],
                    "parent_id": row[2],
                    "path": row[3],
                    "component": row[4],
                    "perms": row[5],
                    "menu_type": row[6],
                    "visible": row[7],
                    "status": row[8],
                    "order_num": row[9],
                })
            
            return permissions
            
        except Exception as e:
            log.error(f"获取角色权限失败: roles={roles}, error={e}")
            return []
    
    @staticmethod
    async def get_user_permissions_from_redis(user_id: int) -> Optional[List[str]]:
        """
        从Redis获取用户权限列表 (优化版本，避免MySQL查询)

        :param user_id: 用户ID
        :return: 权限列表
        """
        try:
            # 1. 查找用户对应的token
            keys = await redis_client.keys("login_tokens:*")

            for key in keys:
                user_data = await redis_client.get(key)
                if not user_data:
                    continue

                # 检查是否是目标用户的数据
                # 这里需要根据实际的数据结构来判断用户ID
                # 暂时通过简单的字符串匹配
                if f'"userId":{user_id}' in user_data or f'"user_id":{user_id}' in user_data:
                    # 提取权限信息
                    perm_match = re.search(r'"permissions":Set\[(.*?)\]', user_data)
                    if perm_match:
                        permissions_str = perm_match.group(1)
                        permissions = re.findall(r'"([^"]+)"', permissions_str)
                        log.debug(f"从Redis获取用户权限: user_id={user_id}, permissions={permissions}")
                        return permissions

            log.warning(f"Redis中未找到用户权限数据: user_id={user_id}")
            return None

        except Exception as e:
            log.error(f"从Redis获取用户权限失败: user_id={user_id}, error={e}")
            return None

    @staticmethod
    async def check_user_permission(user_id: int, permission_code: str) -> bool:
        """
        检查用户是否有特定权限 (优化版本：优先使用Redis)

        :param user_id: 用户ID
        :param permission_code: 权限代码 (如: system:user:list)
        :return: 是否有权限
        """
        try:
            # 1. 优先从Redis获取权限 (性能更好)
            permissions = await JavaPermissionService.get_user_permissions_from_redis(user_id)

            if permissions:
                # 使用Redis权限数据进行检查
                # 支持通配符权限 (如: *:*:*)
                if "*:*:*" in permissions:
                    log.debug(f"Redis权限检查通过(admin): user_id={user_id}, permission={permission_code}")
                    return True

                # 精确匹配
                if permission_code in permissions:
                    log.debug(f"Redis权限检查通过(精确): user_id={user_id}, permission={permission_code}")
                    return True

                # 模糊匹配 (如: system:user:* 匹配 system:user:list)
                for perm in permissions:
                    if perm.endswith("*"):
                        prefix = perm[:-1]
                        if permission_code.startswith(prefix):
                            log.debug(f"Redis权限检查通过(模糊): user_id={user_id}, permission={permission_code}")
                            return True

                log.debug(f"Redis权限检查失败: user_id={user_id}, permission={permission_code}")
                return False

            # 2. Redis获取失败，回退到MySQL查询
            log.info(f"Redis权限获取失败，使用MySQL回退: user_id={user_id}")
            permission_info = await JavaPermissionService.get_user_permissions(user_id)

            if "error" in permission_info:
                return False

            # 检查权限代码
            permission_codes = permission_info.get("permission_codes", [])

            # 支持通配符权限 (如: *:*:*)
            if "*:*:*" in permission_codes:
                return True

            # 精确匹配
            if permission_code in permission_codes:
                return True

            # 模糊匹配 (如: system:user:* 匹配 system:user:list)
            for code in permission_codes:
                if code.endswith("*"):
                    prefix = code[:-1]
                    if permission_code.startswith(prefix):
                        return True

            return False

        except Exception as e:
            log.error(f"检查用户权限失败: user_id={user_id}, permission={permission_code}, error={e}")
            return False
    
    @staticmethod
    async def check_user_menu_access(user_id: int, menu_path: str) -> bool:
        """
        检查用户是否可以访问特定菜单路径

        :param user_id: 用户ID
        :param menu_path: 菜单路径 (如: /system/user)
        :return: 是否可以访问
        """
        try:
            permission_info = await JavaPermissionService.get_user_permissions(user_id)

            if "error" in permission_info:
                return False

            # 超级管理员可以访问所有菜单
            if permission_info.get("is_admin", False):
                return True

            # 检查菜单路径
            menu_paths = permission_info.get("menu_paths", [])

            # 精确匹配
            if menu_path in menu_paths:
                return True

            # 检查父路径权限
            for path in menu_paths:
                if path and menu_path.startswith(path):
                    return True

            return False

        except Exception as e:
            log.error(f"检查菜单访问权限失败: user_id={user_id}, menu_path={menu_path}, error={e}")
            return False


# 创建服务实例
java_permission_service = JavaPermissionService()
