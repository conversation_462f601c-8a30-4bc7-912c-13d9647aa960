<template>
    <div class="system-dic-dialog-container">
        <el-dialog style="position: absolute; top: 100px;" :title="state.dialog.title"
            v-model="state.dialog.isShowDialog" width="800">
            <div class="line"></div>
            <el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules" size="default"
                label-width="90px">
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="场景名称" prop="sceneName">
                            <el-input v-model="state.ruleForm.sceneName" placeholder="请输入场景名称" />
                        </el-form-item>
                        <el-form-item label="场景状态">
                            <el-switch v-model="state.ruleForm.enable" :active-value="1" :inactive-value="2" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="备注信息" prop="remark">
                            <el-input v-model="state.ruleForm.remark" type="textarea" :rows="4" placeholder="请输入内容" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="line" style="margin: 30px 0;"></div>

                <div class="condition-wrap">
                    <el-form-item label="触发器" prop="triggers">
                        <div class="item-wrap" style="background-color: #eef3f7;width: 100%;padding: 15px 10px;">
                            <el-row :gutter="16">
                                <el-col :span="24">
                                    <span>触发条件：</span>
                                    <el-select v-model="state.ruleForm.cond" placeholder="请选择触发条件" size="default"
                                        style="width: 160px">
                                        <el-option key="1" label="任意条件" :value="1" />
                                        <el-option key="2" label="所有条件" :value="2" />
                                        <el-option key="3" label="不满足" :value="3"
                                            :disabled="formJson.triggers.length > 1" />
                                    </el-select>
                                    <span style="margin-left: 20px; font-size: 12px">提示：触发器有且只有一个定时，执行动作中的告警无效</span>
                                </el-col>
                            </el-row>
                        </div>
                        <div class="item-wrap" v-for="(item, index) in formJson.triggers" :key="index">
                            <el-row :gutter="16">
                                <el-col :span="5">
                                    <el-select v-model="item.source" placeholder="请选择" size="default"
                                        @change="handleTriggerSource($event, index)">
                                        <el-option v-for="subItem in triggerSource" :key="subItem.value"
                                            :label="subItem.label" :value="subItem.value"></el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="10" v-if="item.source == 1">
                                    <el-input readonly v-model="item.deviceCount" size="default" placeholder="请选择设备"
                                        style="margin-top: 2px">
                                        <template #prepend>
                                            <span disabled>数量</span>
                                        </template>
                                        <template #append>
                                            <el-button @click="handleSelectDevice('trigger', item, index)"
                                                size="default">选择设备</el-button>
                                        </template>
                                    </el-input>
                                </el-col>
                                <el-col :span="10" v-if="item.source == 3">
                                    <el-input readonly v-model="item.productName" size="default" placeholder="请选择产品"
                                        style="margin-top: 2px">
                                        <template #append>
                                            <el-button @click="handleSelectProduct('trigger', item, index)"
                                                size="default">选择产品</el-button>
                                        </template>
                                    </el-input>
                                </el-col>
                                <div class="delete-wrap" v-if="index != 0">
                                    <el-button size="default" plain type="danger" style="padding: 6px;font-size: 13px;"
                                        @click="handleRemoveTrigger(index)"><el-icon>
                                            <Delete />
                                        </el-icon>删除</el-button>
                                </div>
                            </el-row>

                            <!--定时-->
                            <el-row :gutter="16" v-if="item.source == 2">
                                <el-col :span="5">
                                    <el-time-picker style="width: 100%" v-model="item.timerTimeValue" size="default"
                                        value-format="HH:mm" format="HH:mm" placeholder="选择时间"
                                        @change="timeChange($event, index)"
                                        :disabled="item.isAdvance == 1"></el-time-picker>
                                </el-col>
                                <el-col :span="19">
                                    <el-select v-model="item.timerWeekValue" placeholder="请选择" multiple
                                        style="width: 100%" @change="weekChange($event, index)" size="default"
                                        :disabled="item.isAdvance == 1">
                                        <el-option v-for="subItem in timerWeeks" :key="subItem.value"
                                            :label="subItem.label" :value="subItem.value"></el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="5" style="margin-top: 8px">
                                    <el-checkbox v-model="item.isAdvance" :true-value="1" :false-label="0"
                                        @change="customerCronChange()" border size="default" style="width: 100%">
                                        <div style="font-size: 12px;margin-top: 2px;">自定义CRON</div>
                                    </el-checkbox>
                                </el-col>
                                <el-col :span="19" style="margin-top: 8px">
                                    <el-input v-model="item.cronExpression" placeholder="cron执行表达式"
                                        :disabled="item.isAdvance == 0" size="default">
                                        <template #append>
                                            <el-button type="primary" @click="handleShowCron(item, index)"
                                                :disabled="item.isAdvance == 0">
                                                生成表达式
                                                <i class="el-icon-time el-icon--right"></i>
                                            </el-button>
                                        </template>
                                    </el-input>
                                </el-col>
                            </el-row>
                            <div v-if="item.thingsModel">
                                <!--类型和父级物模型-->
                                <!-- {{ item.thingsModel }} -->
                                <el-row :gutter="16">
                                    <el-col :span="5" v-if="(item.thingsModel.properties && item.thingsModel.properties.length > 0) ||
                                        (item.thingsModel.functions && item.thingsModel.properties.length > 0) ||
                                        (item.thingsModel.events && item.thingsModel.events.length > 0)
                                    ">
                                        <el-select v-model="item.type" placeholder="请选择类型" size="default"
                                            @change="handleTriggerTypeChange($event, index)">
                                            <el-option v-for="(subItem, subIndex) in triggerTypes"
                                                :key="subIndex + 'type'" :label="subItem.label"
                                                :value="subItem.value"></el-option>
                                        </el-select>
                                    </el-col>

                                    <el-col :span="10">
                                        <el-select style="width: 100%" v-model="item.parentId" placeholder="请选择父级物模型"
                                            size="default"
                                            v-if="item.type == 1 && (item.thingsModel.properties && item.thingsModel.properties.length > 0)"
                                            @change="handleTriggerParentModelChange($event, index)">
                                            <el-option v-for="(subItem, subIndex) in item.thingsModel.properties"
                                                :key="subIndex + 'triggerProperty'" :label="subItem.name"
                                                :value="subItem.id"></el-option>
                                        </el-select>
                                        <el-select style="width: 100%" v-model="item.parentId" placeholder="请选择父级物模型"
                                            size="default"
                                            v-else-if="item.type == 2 && (item.thingsModel.functions && item.thingsModel.functions.length > 0)"
                                            @change="handleTriggerParentModelChange($event, index)">
                                            <el-option v-for="(subItem, subIndex) in item.thingsModel.functions"
                                                :key="subIndex + 'triggerFunc'" :label="subItem.name"
                                                :value="subItem.id"></el-option>
                                        </el-select>
                                        <el-select style="width: 100%" v-model="item.parentId" placeholder="请选择父级物模型"
                                            size="default"
                                            v-else-if="item.type == 3 && (item.thingsModel.events && item.thingsModel.events.length > 0)"
                                            @change="handleTriggerParentModelChange($event, index)">
                                            <el-option v-for="(subItem, subIndex) in item.thingsModel.events"
                                                :key="subIndex + 'triggerEvents'" :label="subItem.name"
                                                :value="subItem.id"></el-option>
                                        </el-select>
                                    </el-col>
                                </el-row>

                                <!--数组索引/物模型/操作符和值-->
                                <el-row :gutter="16">
                                    <el-col :span="5"
                                        v-if="item.parentModel && item.parentModel.datatype.type === 'array'">
                                        <el-select v-model="item.arrayIndex" placeholder="请选择" size="default"
                                            @change="handleTriggerIndexChange($event, index)">
                                            <el-option v-for="subItem in item.parentModel.datatype.arrayModel"
                                                :key="subItem.id" :label="subItem.name" :value="subItem.id"></el-option>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="5"
                                        v-if="item.parentModel && item.parentModel.datatype.type === 'array' && item.parentModel.datatype.arrayType === 'object'">
                                        <el-select v-model="item.id" placeholder="请选择" size="default"
                                            @change="handleTriggerModelChange($event, index)">
                                            <el-option v-for="(subItem, subIndex) in item.parentModel.datatype.params"
                                                :key="subIndex" :label="subItem.name" :value="subItem.id"></el-option>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="5"
                                        v-if="item.parentModel && item.parentModel.datatype.type === 'object'">
                                        <el-select v-model="item.id" placeholder="请选择" size="default"
                                            @change="handleTriggerModelChange($event, index)">
                                            <el-option v-for="(subItem, subIndex) in item.parentModel.datatype.params"
                                                :key="subIndex" :label="subItem.name" :value="subItem.id"></el-option>
                                        </el-select>
                                    </el-col>
                                    <!-- {{ item }} -->
                                    <el-col :span="5"
                                        v-if="item.model && item.model.datatype && (item.model.datatype.type == 'integer' || item.model.datatype.type == 'decimal' || item.model.datatype.type == 'string')">
                                        <el-select v-model="item.operator" placeholder="选择操作符" size="default">
                                            <el-option key="=" label="等于（=）" value="=" />
                                            <el-option key="!=" label="不等于（!=）" value="!=" />
                                            <el-option key=">" label="大于（>）" value=">"
                                                v-if="item.model.datatype.type === 'integer' || item.model.datatype.type === 'decimal'" />
                                            <el-option key="<" label="小于（<）" value="<"
                                                v-if="item.model.datatype.type === 'integer' || item.model.datatype.type === 'decimal'" />
                                            <el-option key=">=" label="大于等于（>=）" value=">="
                                                v-if="item.model.datatype.type === 'integer' || item.model.datatype.type === 'decimal'" />
                                            <el-option key="<=" label="小于等于（<=）" value="<="
                                                v-if="item.model.datatype.type === 'integer' || item.model.datatype.type === 'decimal'" />
                                            <el-option key="between" label="在...之间（between）" value="between"
                                                v-if="item.model.datatype.type === 'integer' || item.model.datatype.type === 'decimal'" />
                                            <el-option key="notBetween" label="不在...之间（notBetween）" value="notBetween"
                                                v-if="item.model.datatype.type === 'integer' || item.model.datatype.type === 'decimal'" />
                                            <el-option key="contain" label="包含（contain）" value="contain"
                                                v-if="item.model.datatype.type === 'string'" />
                                            <el-option key="notContain" label="不包含（notContain）" value="notContain"
                                                v-if="item.model.datatype.type === 'string'" />
                                        </el-select>
                                    </el-col>
                                    <el-col :span="9" v-if="item.model">
                                        <div v-if="item.model.datatype && (item.model.datatype.type === 'integer' || item.model.datatype.type === 'decimal')"
                                            v-show="item.operator === 'between' || item.operator === 'notBetween'">
                                            <el-input style="width: 95px; vertical-align: baseline"
                                                @input="valueChange($event, item)" v-model="item.valueA" placeholder="值"
                                                :max="item.model.datatype.max" :min="item.model.datatype.min"
                                                type="number" size="default"></el-input>
                                            <span style="padding: 0 3px; color: #999">-</span>
                                            <el-input style="width: 165px; vertical-align: baseline"
                                                @input="valueChange($event, item)" v-model="item.valueB" placeholder="值"
                                                :max="item.model.datatype.max" :min="item.model.datatype.min"
                                                type="number" size="default">
                                                <template slot="append">{{ item.model.datatype.unit }}</template>
                                            </el-input>
                                        </div>

                                        <div v-if="item.model.datatype && (item.model.datatype.type === 'integer' || item.model.datatype.type === 'decimal')"
                                            v-show="item.operator !== 'between' && item.operator !== 'notBetween'">
                                            <el-input style="vertical-align: baseline" v-model="item.value"
                                                placeholder="值" size="default">
                                                <template slot="append">{{ item.model.datatype.unit }}</template>
                                            </el-input>
                                        </div>
                                        <div v-else-if="item.model.datatype && (item.model.datatype.type === 'bool')">
                                            <el-switch style="vertical-align: sub" v-model="item.value"
                                                :active-text="item.model.datatype.trueText"
                                                :inactive-text="item.model.datatype.falseText" active-value="1"
                                                inactive-value="0" size="mini"></el-switch>
                                        </div>
                                        <div v-else-if="item.model.datatype && (item.model.datatype.type === 'enum')">
                                            <el-select v-model="item.value" placeholder="请选择" style="width: 100%"
                                                size="default">
                                                <el-option v-for="(subItem, subIndex) in item.model.datatype.enumList"
                                                    :key="subIndex + 'things'" :label="subItem.text"
                                                    :value="subItem.value"></el-option>
                                            </el-select>
                                        </div>
                                        <div v-else-if="item.model.datatype && (item.model.datatype.type === 'string')">
                                            <el-input v-model="item.value" placeholder="请输入字符串" size="default"
                                                :max="item.model.datatype.maxLength" />
                                        </div>
                                    </el-col>
                                </el-row>
                            </div>
                        </div>
                        <div v-if="!(state.ruleForm.cond == 3 && formJson.triggers.length > 0)">
                            +
                            <a style="color: #409eff" @click="handleAddTrigger()">添加触发器</a>
                        </div>
                    </el-form-item>
                </div>

                <div class="line" style="margin: 30px 0;"></div>

                <div class="action-wrap">
                    <el-form-item label="执行动作">
                        <div class="item-wrap" style="background-color: #eef3f7">
                            <el-row :gutter="16">
                                <el-col :span="8">
                                    <span class="flex" style="align-items: center;">
                                        <el-tooltip class="item" effect="dark" content="在设定的时间范围内将不再重复执行"
                                            placement="top">
                                            <el-icon>
                                                <QuestionFilled />
                                            </el-icon>
                                        </el-tooltip>
                                        <span class="ml2">静默时间：</span>
                                    </span>
                                    <el-input v-model="state.ruleForm.silentPeriod" size="default" placeholder="分钟"
                                        type="number" style="width: 140px">
                                        <template slot="append">分钟</template>
                                    </el-input>
                                </el-col>
                                <el-col :span="8">
                                    <span>执行方式：</span>
                                    <el-select v-model="state.ruleForm.executeMode" placeholder="请选择执行方式" size="default"
                                        style="width: 160px">
                                        <el-option key="1" label="串行(顺序执行)" :value="1" />
                                        <el-option key="2" label="并行(同时执行)" :value="2" />
                                    </el-select>
                                </el-col>
                                <el-col :span="8">
                                    <span class="flex" style="align-items: center;">
                                        <el-tooltip class="item" effect="dark" content="延时不会存储，限制为90秒内" placement="top">
                                            <el-icon>
                                                <QuestionFilled />
                                            </el-icon>
                                        </el-tooltip>
                                        <span class="ml2">延时执行： </span>
                                    </span>
                                    <el-input v-model="state.ruleForm.executeDelay" size="default" prop="executeDelay"
                                        placeholder="秒钟" :max="90" :min="0"
                                        oninput="if(value>90)value=90;if(value<0)value=0" type="number"
                                        style="width: 140px">
                                        <template slot="append">秒钟</template>
                                    </el-input>
                                </el-col>
                            </el-row>
                        </div>

                        <div class="item-wrap" v-for="(item, index) in formJson.actions" :key="index">
                            <el-row :gutter="16">
                                <el-col :span="5">
                                    <el-select v-model="item.source" placeholder="请选择" size="default"
                                        style="margin-top: 1px" @change="handleActionSourceChange($event, index)">
                                        <el-option v-for="subItem in actionSource" :key="subItem.value"
                                            :label="subItem.label" :value="subItem.value"></el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="10" v-if="item.source == 1">
                                    <el-input readonly v-model="item.deviceCount" size="default" placeholder="请选择设备"
                                        style="margin-top: 2px">
                                        <template #prepend>
                                            <span disabled>数量</span>
                                        </template>
                                        <template #append>
                                            <el-button @click="handleSelectDevice('action', item, index)"
                                                size="default">选择设备</el-button>
                                        </template>

                                    </el-input>
                                </el-col>
                                <el-col :span="10" v-if="item.source == 3">
                                    <el-input readonly v-model="item.productName" size="default" placeholder="请选择产品"
                                        style="margin-top: 2px">
                                        <template #append>
                                            <el-button @click="handleSelectProduct('action', item, index)"
                                                size="default">选择产品</el-button>
                                        </template>
                                    </el-input>
                                </el-col>
                                <div class="delete-wrap">
                                    <el-button size="default" v-if="index != 0" plain type="danger" style="padding: 5px"
                                        @click="handleRemoveAction(index)"><el-icon>
                                            <Delete />
                                        </el-icon>删除</el-button>
                                </div>
                            </el-row>
                            <div v-if="item.thingsModel">
                                <!--类型和父级物模型-->
                                <!-- {{ item.thingsModel }} -->
                                <el-row :gutter="16">
                                    <el-col :span="5" v-if="(item.thingsModel.properties && item.thingsModel.properties.length > 0) ||
                                        (item.thingsModel.functions && item.thingsModel.functions.length > 0)
                                    ">
                                        <el-select v-model="item.type" placeholder="请选择类型" size="default"
                                            @change="handleActionTypeChange($event, index)">
                                            <el-option v-for="(subItem, subIndex) in actionTypes"
                                                :key="subIndex + 'type'" :label="subItem.label"
                                                :value="subItem.value"></el-option>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="10">
                                        <el-select style="width: 100%" v-model="item.parentId" placeholder="请选择父级物模型"
                                            size="default"
                                            v-if="item.type == 1 && (item.thingsModel.properties && item.thingsModel.properties.length > 0)"
                                            @change="handleActionParentModelChange($event, index)">
                                            <el-option v-for="(subItem, subIndex) in item.thingsModel.properties"
                                                :key="subIndex + 'actionProperty'" :label="subItem.name"
                                                :value="subItem.id"></el-option>
                                        </el-select>
                                        <el-select style="width: 100%" v-model="item.parentId" placeholder="请选择父级物模型"
                                            size="default"
                                            v-else-if="item.type == 2 && (item.thingsModel.functions && item.thingsModel.functions.length > 0)"
                                            @change="handleActionParentModelChange($event, index)">
                                            <el-option v-for="(subItem, subIndex) in item.thingsModel.functions"
                                                :key="subIndex + 'actionFunc'" :label="subItem.name"
                                                :value="subItem.id"></el-option>
                                        </el-select>
                                    </el-col>
                                </el-row>

                                <!--数组索引/物模型/值-->
                                <el-row :gutter="16">
                                    <el-col :span="5"
                                        v-if="item.parentModel.datatype && item.parentModel.datatype.type === 'array'">
                                        <el-select v-model="item.arrayIndex" placeholder="请选择" size="default"
                                            @change="handleActionIndexChange($event, index)">
                                            <el-option v-for="subItem in item.parentModel.datatype.arrayModel"
                                                :key="subItem.id" :label="subItem.name" :value="subItem.id"></el-option>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="5"
                                        v-if="item.parentModel.datatype && item.parentModel.datatype.type === 'array' && item.parentModel.datatype.arrayType === 'object'">
                                        <el-select v-model="item.id" placeholder="请选择" size="default"
                                            @change="handleActionModelChange($event, index)">
                                            <el-option v-for="(subItem, subIndex) in item.parentModel.datatype.params"
                                                :key="subIndex" :label="subItem.name" :value="subItem.id"></el-option>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="5"
                                        v-if="item.parentModel.datatype && item.parentModel.datatype.type === 'object'">
                                        <el-select v-model="item.id" placeholder="请选择" size="default"
                                            @change="handleActionModelChange($event, index)">
                                            <el-option v-for="(subItem, subIndex) in item.parentModel.datatype.params"
                                                :key="subIndex" :label="subItem.name" :value="subItem.id"></el-option>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="10" v-if="item.model">
                                        <div
                                            v-if="item.model.datatype && (item.model.datatype.type == 'integer' || item.model.datatype.type == 'decimal')">
                                            <el-input style="vertical-align: baseline" v-model="item.value"
                                                placeholder="值" :max="item.model.datatype.max"
                                                :min="item.model.datatype.min" type="number" size="default">
                                                <template #append>{{ item.model.datatype.unit }}</template>
                                            </el-input>
                                        </div>
                                        <div v-else-if="item.model.datatype && (item.model.datatype.type == 'bool')">
                                            <el-switch style="vertical-align: baseline" v-model="item.value"
                                                :active-text="item.model.datatype.trueText"
                                                :inactive-text="item.model.datatype.falseText" active-value="1"
                                                inactive-value="0"></el-switch>
                                        </div>
                                        <div v-else-if="item.model.datatype && (item.model.datatype.type == 'enum')">
                                            <el-select v-model="item.value" placeholder="请选择" style="width: 100%"
                                                size="default">
                                                <el-option v-for="(subItem, subIndex) in item.model.datatype.enumList"
                                                    :key="subIndex + 'things'" :label="subItem.text"
                                                    :value="subItem.value"></el-option>
                                            </el-select>
                                        </div>
                                        <div v-else-if="item.model.datatype && (item.model.datatype.type == 'string')">
                                            <el-input v-model="item.value" placeholder="请输入字符串" size="default"
                                                :max="item.model.datatype.maxLength" />
                                        </div>
                                    </el-col>
                                </el-row>
                            </div>
                        </div>
                        <div>
                            +
                            <a style="color: #409eff" @click="handleAddAction()">添加执行动作</a>
                        </div>
                    </el-form-item>
                </div>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="onSubmit(DialogFormRef)" v-auths="['iot:scene:edit']"
                    v-show="state.ruleForm.sceneId" :disabled="updateBtnDisabled">修
                    改</el-button>
                <el-button type="primary" @click="onSubmit(DialogFormRef)" v-auths="['iot:scene:add']"
                    v-show="!state.ruleForm.sceneId" :disabled="updateBtnDisabled">新
                    增</el-button>
                <el-button @click="onCancel">取 消</el-button>
            </template>
        </el-dialog>
        <DeviceList ref="DeviceListRef" @deviceEvent="getSelectProductDevice($event, 1)" />
        <ProductList ref="ProductListRef" @productEvent="getSelectProductDevice($event, 2)" />

    </div>
</template>

<script setup lang="ts" name="NoticeDialogRef">
import { defineAsyncComponent, reactive, ref } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { cacheJsonThingsModel } from '/@/api/iot/template';
import { addScene, getScene, updateScene } from '/@/api/iot/scene';
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const DialogFormRef = ref();
// 引入组件
const ProductList = defineAsyncComponent(() => import('/@/views/iot/scene/product-list.vue'));
const DeviceList = defineAsyncComponent(() => import('/@/views/iot/scene/device-list.vue'));

// 定义变量内容
const ProductListRef = ref();
const DeviceListRef = ref();
const currentType = ref()// 当前选择设备时的类型（trigger/action）
const currentIndex = ref()// 当前选择设备时的类型索引
const triggerIndex = ref(0) // 触发器的索引，用于接收传入的表达式
const expression = ref('') // cron表达式
const openCron = ref(false) // 是否打开cron表达式弹窗
const updateBtnDisabled = ref(false) // 更新按钮是否禁用
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref() //socialPlatformId
const initialState = {
    ruleForm: {
        sceneName: '',
        enable: 1,
        remark: '',
        cond: 1,
        sceneId: '' as any,
        silentPeriod: 0 as any,
        executeMode: 1 as any,
        executeDelay: 0 as any,
        hasAlert: 2, // 1=包含告警，2=不包含告警
        triggers: [] as any,
        actions: [] as any,
        applicationName: "fastbee"


    },
    dialog: {
        isShowDialog: false,
        type: '',
        title: '',
        submitTxt: '',
    },
}
let formJson = reactive({
    triggers: [
        {
            productId: 0,
            productName: '',
            deviceCount: 0,
            deviceNums: [],
            source: 1, //1=设备，2=定时，3=产品
            type: 1, // 类型1=属性/2=功能，3=事件
            parentId: '', // 物模父级id
            parentName: '',
            parentModel: {
                datatype: {
                    type: '',
                    arrayModel: [] as any,
                    arrayType: '',
                    params: [] as any,
                    maxLength: -1 as any,
                }
            } as any,
            model: {
                datatype: {
                    type: '',
                    arrayModel: [] as any,
                    arrayType: '',
                    params: [] as any,
                    max: '' as any,
                    min: '' as any,
                    unit: '' as any,
                    trueText: '' as any,
                    falseText: '' as any,
                    enumList: [] as any,
                    maxLength: '' as any,
                }
            },
            operator: '',
            id: '',
            name: '',
            value: '', // between操作符时，值=值A-值B
            valueA: '',
            valueB: '',
            arrayIndex: '', // 索引，数组才有
            arrayIndexName: '',
            isAdvance: 0, // 自定义CRON
            cronExpression: '', // cron表达式
            timerTimeValue: '', // 时间
            timerWeekValue: [1, 2, 3, 4, 5, 6, 7], // 星期
            scriptPurpose: 3, // scriptPurpose:脚本用途(1=数据流，2=触发器，3=执行动作),
            thingsModel: {
                properties: [] as any,
                functions: [] as any as any,
                events: [] as any as any,
            }, // 添加 thingsModel 属性
        },
    ],
    actions: [
        // {
        //     productId: 0,
        //     productName: '',
        //     deviceCount: 0,
        //     deviceNums: [],
        //     source: 1, //1=设备，3=产品，4=告警
        //     type: 2, // 类型1=属性/2=功能，3=事件
        //     parentId: '', // 物模父级id
        //     parentName: '',
        //     parentModel: null, // 父级物模型
        //     model: null, // 物模型
        //     id: '',
        //     name: '',
        //     value: '',
        //     arrayIndex: '', // 索引，数组才有
        //     arrayIndexName: '',
        //     scriptPurpose: 3, // scriptPurpose:脚本用途(1=数据流，2=触发器，3=执行动作)
        //     thingsModel: null, // 添加 thingsModel 属性
        // },
        {
            productId: 0,
            productName: '',
            deviceCount: 0,
            deviceNums: [],
            source: 1,
            type: 2,
            parentId: '',
            parentName: '',
            parentModel: {
                datatype: {
                    type: '',
                    arrayModel: [] as any,
                    arrayType: '',
                    params: [] as any,
                    max: '' as any,
                    min: '' as any,
                    unit: '' as any,
                    trueText: '' as any,
                    falseText: '' as any,
                    enumList: [] as any,
                    maxLength: '' as any,

                }
            },
            model: {
                datatype: {
                    type: '',
                    arrayModel: [] as any,
                    arrayType: '',
                    params: [] as any,
                    max: '' as any,
                    min: '' as any,
                    unit: '' as any,
                    trueText: '' as any,
                    falseText: '' as any,
                    enumList: [] as any,
                    maxLength: '' as any,
                }
            },
            id: '',
            name: '',
            value: '',
            arrayIndex: '',
            arrayIndexName: '',
            scriptPurpose: 3,
            thingsModel: {
                properties: [] as any,
                functions: [] as any,
                events: [] as any,
            },
            valueA: '', // Add valueA property
            valueB: '', // Add valueB property
            operator: '',
            timerTimeValue: '',
            timerWeekValue: []

        }
    ],
}) // 表单参数
const timerWeeks = reactive([
    {
        value: 1,
        label: '周一',
    },
    {
        value: 2,
        label: '周二',
    },
    {
        value: 3,
        label: '周三',
    },
    {
        value: 4,
        label: '周四',
    },
    {
        value: 5,
        label: '周五',
    },
    {
        value: 6,
        label: '周六',
    },
    {
        value: 7,
        label: '周日',
    },
])
const triggerSource = reactive([
    {
        value: 1,
        label: '设备触发',
    },
    {
        value: 2,
        label: '定时触发',
    },
    {
        value: 3,
        label: '产品触发',
    },
]) // 触发器源 1=设备，2=定时
const triggerTypes = reactive([
    {
        value: 1,
        label: '属性',
    },
    {
        value: 2,
        label: '功能',
    },
    {
        value: 3,
        label: '事件',
    },
    {
        value: 5,
        label: '设备上线',
    },
    {
        value: 6,
        label: '设备下线',
    },
])// 触发器类别
const actionSource = reactive([
    {
        value: 1,
        label: '设备执行',
    },
    {
        value: 3,
        label: '产品执行',
    },
    {
        value: 4,
        label: '告警执行',
    },
])// 动作源 1=设备，3=产品执行，4=告警执行
const actionTypes = reactive([
    {
        value: 1,
        label: '属性',
    },
    {
        value: 2,
        label: '功能',
    },
])// 动作类别
// 初始化 state
const state = reactive({
    ruleForm: { ...initialState.ruleForm },
    dialog: { ...initialState.dialog },
});

// 校验规则
const rules = reactive({
    sceneName: [
        {
            required: true,
            message: '场景名称不能为空',
            trigger: 'blur',
        },
    ],
    executeDelay: [
        {
            required: true,
            max: 90,
            min: 0,
            message: '延时0-90',
            trigger: 'blur',
        },
    ],

})
// 触发器中，选择between操作符，值是A值和B值用中划线分割
const valueChange = (value: any, item: any) => {
    item.value = item.valueA + '-' + item.valueB;
}
// 打开弹窗
const openDialog = (type: string, row: any, sceneId: string) => {
    console.log(row, sceneId);

    if (type == 'edit') {
        if (row != undefined) {
            getScene(row.sceneId).then(response => {
                state.ruleForm = response.data.data
                // triggers赋值
                formJson.triggers = state.ruleForm.triggers;
                //     if (formJson.triggers[currentIndex.value].thingsModel.properties) {

                //     // formJson.triggers[currentIndex.value].thingsModel.properties = formJson.triggers[currentIndex.value].thingsModel.properties.filter((item: any) => item.isMonitor == 0 && item.isReadonly == 0);

                // }
                for (let i = 0; i < formJson.triggers.length; i++) {
                    // 定时
                    if (formJson.triggers[i].source == 2) {
                        if (formJson.triggers[i].isAdvance == 0) {
                            let arrayValue = formJson.triggers[i].cronExpression.substring(12).split(',').map(Number);
                            formJson.triggers[i].timerWeekValue = arrayValue;
                            let timerTimeValue = formJson.triggers[i].cronExpression.substring(5, 7) + ':' + formJson.triggers[i].cronExpression.substring(2, 4);
                            // 解决时间选择器不能编辑问题
                            formJson.triggers[i].timerTimeValue = timerTimeValue;
                        }

                    } else {
                        // 获取设备和物模型详情
                        formatSceneScript(formJson.triggers[i], i);
                    }
                }
                // actions赋值
                formJson.actions = state.ruleForm.actions;
                console.log(formJson, 'formJson');

                for (let i = 0; i < formJson.actions.length; i++) {
                    // 获取设备和物模型详情
                    formatSceneScript(formJson.actions[i], i);
                }
                // 按钮可用
                setTimeout(() => {
                    updateBtnDisabled.value = false;
                }, 2000);
            });

        } else {
            getScene(row.sceneId).then(response => {
                console.log(response.data.data);
                state.ruleForm = response.data.data
                // triggers赋值
                formJson.triggers = state.ruleForm.triggers;
                for (let i = 0; i < formJson.triggers.length; i++) {
                    // 定时
                    if (formJson.triggers[i].source == 2) {
                        if (formJson.triggers[i].isAdvance == 0) {
                            let arrayValue = formJson.triggers[i].cronExpression.substring(12).split(',').map(Number);
                            formJson.triggers[i].timerWeekValue = arrayValue;
                            let timerTimeValue = formJson.triggers[i].cronExpression.substring(5, 7) + ':' + formJson.triggers[i].cronExpression.substring(2, 4);
                            // 解决时间选择器不能编辑问题
                            formJson.triggers[i].timerTimeValue = timerTimeValue;
                        }
                    } else {
                        // 获取设备和物模型详情
                        formatSceneScript(formJson.triggers[i], i);
                    }
                }
                // actions赋值
                formJson.actions = state.ruleForm.actions;
                for (let i = 0; i < formJson.actions.length; i++) {
                    // 获取设备和物模型详情
                    formatSceneScript(formJson.actions[i], i);
                }
                // 按钮可用
                setTimeout(() => {
                    updateBtnDisabled.value = false;
                }, 2000);
            });
        }

        state.dialog.title = '修改场景联动';
        state.dialog.submitTxt = '修 改';
    } else {
        state.dialog.title = '新增场景联动';
        state.dialog.submitTxt = '新 增';
        resetState();
    }

    state.dialog.isShowDialog = true;

};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            let triggers = [];
            let actions = [];
            // 触发器验证和构建
            for (let i = 0; i < formJson.triggers.length; i++) {
                // 类型为属性/功能/事件
                if (formJson.triggers[i].type == 1 || formJson.triggers[i].type == 2 || formJson.triggers[i].type == 3) {
                    // 值不能为空
                    if (formJson.triggers[i].source == 1) {
                        if (formJson.triggers[i].value == '') {
                            ElMessage.error('触发器中的选项和值不能为空');
                            return;
                        } else if (formJson.triggers[i].value.indexOf('-') != -1) {
                            // 区间格式10-50
                            if (formJson.triggers[i].valueA == '' || formJson.triggers[i].valueB == '') {
                                ElMessage.error('触发器中区间值不能为空');
                                return;
                            }
                        }
                    }
                    // 定时时间不能为空
                    if (formJson.triggers[i].source == 2) {
                        if (formJson.triggers[i].isAdvance == 0) {
                            if (formJson.triggers[i].timerTimeValue == '' || formJson.triggers[i].timerTimeValue == null) {
                                ElMessage.error('执行时间不能空');
                                return;
                            }
                            if (formJson.triggers[i].timerWeekValue == null || formJson.triggers[i].timerWeekValue == '' as any) {
                                ElMessage.error('请选择要执行的星期');
                                return;
                            }
                        } else if (formJson.triggers[i].isAdvance == 1) {
                            if (formJson.triggers[i].cronExpression == '') {
                                ElMessage.error('cron表达式不能为空');
                                return;
                            }
                        }
                    }
                }
                // 数据重组
                let item = formJson.triggers[i];
                // id拼接array索引
                if (item.arrayIndex != '') {
                    item.id = 'array_' + item.arrayIndex + '_' + item.id;
                }
                // 只传需要的数据
                triggers[i] = {
                    productId: item.productId,
                    productName: item.productName,
                    deviceNums: item.deviceNums,
                    deviceCount: item.deviceCount,
                    source: item.source,
                    type: item.type,
                    id: item.id,
                    name: item.name,
                    operator: item.operator,
                    value: item.value,
                    isAdvance: item.isAdvance,
                    cronExpression: item.cronExpression,
                    parentId: item.parentId,
                    parentName: item.parentName,
                    arrayIndex: item.arrayIndex,
                    arrayIndexName: item.arrayIndexName,
                    scriptPurpose: 2, // scriptPurpose:脚本用途(1=数据流，2=触发器，3=执行动作)
                };
            }
            // 动作验证和构建
            for (let i = 0; i < formJson.actions.length; i++) {
                if (formJson.actions[i].value === '' && formJson.actions[i].source !== 4) {
                    ElMessage.error('执行动作中的选项和值不能为空');
                    return;
                }
                // 数据重组
                let item = formJson.actions[i];
                // id拼接array索引
                if (item.arrayIndex != '') {
                    item.id = 'array_' + item.arrayIndex + '_' + item.id;
                }
                // 只传需要的数据
                actions[i] = {
                    productId: item.productId,
                    productName: item.productName,
                    deviceCount: item.deviceCount,
                    source: item.source,
                    deviceNums: item.deviceNums,
                    type: item.type,
                    id: item.id,
                    name: item.name,
                    value: item.value,
                    parentId: item.parentId,
                    parentName: item.parentName,
                    arrayIndex: item.arrayIndex,
                    arrayIndexName: item.arrayIndexName,
                    scriptPurpose: 3, // scriptPurpose:脚本用途(1=数据流，2=触发器，3=执行动作)
                };
            }
            // 判断是否包含告警(1=包含，2=不包含)
            if (actions.filter((x) => x.source === 4).length > 0) {
                state.ruleForm.hasAlert = 1;
            } else {
                state.ruleForm.hasAlert = 2;
            }
            state.ruleForm.triggers = triggers;
            state.ruleForm.actions = actions;
            // this.confirmLoading = true;
            if (state.ruleForm.sceneId != '') {
                updateScene(state.ruleForm).then(() => {
                    ElMessage.success('修改成功');
                    state.dialog.isShowDialog = false;
                    // this.confirmLoading = false;
                    emit('refresh');
                });
            } else {
                addScene(state.ruleForm).then(() => {
                    ElMessage.success('新增成功');
                    state.dialog.isShowDialog = false;
                    // this.confirmLoading = false;
                    emit('refresh');
                });
            }

        } else {
            console.log('error submit!', fields)
        }
    })
};
/**
 * 格式化显示场景脚本（触发器和执行动作）
 * sceneScript:触发器或动作
 * sceneScript.scriptPurpose：脚本用途(1=数据流，2=触发器，3=执行动作)
 */
const formatSceneScript = (sceneScript: any, index: any) => {
    if (sceneScript.scriptPurpose == 2) {
        // 获取物模型
        cacheJsonThingsModel(sceneScript.productId).then((response) => {
            let data = JSON.parse(response.data.data);
            sceneScript.thingsModel = formatArrayIndex(data);
            // value赋值，value=valueA-valueB
            if (sceneScript.value.indexOf('-') != -1) {
                let values = sceneScript.value.split('-');
                sceneScript.valueA = values[0];
                sceneScript.valueB = values[1];
            }
            let sceneScripts = [];
            if (sceneScript.type == 1) {
                if (sceneScript.thingsModel.properties) {
                    sceneScript.thingsModel.properties = sceneScript.thingsModel.properties.filter((item: any) => item.type == 1 && item.subType == 3);
                }
                // 属性
                sceneScripts = sceneScript.thingsModel.properties;
            } else if (sceneScript.type == 2) {
                // 功能
                sceneScripts = sceneScript.thingsModel.functions;
            } else if (sceneScript.type == 3) {
                // 事件
                sceneScripts = sceneScript.thingsModel.events;
            }
            // 父级物模型和物模型赋值
            setParentAndModelData(sceneScript, sceneScripts);
            // 解决数组在界面中不更新问题
            formJson.triggers[index] = formJson.triggers[index];

        });
    } else if (sceneScript.scriptPurpose == 3) {
        // 执行告警，没有物模型
        if (sceneScript.source == 4) {
            return;
        }
        // 获取物模型
        cacheJsonThingsModel(sceneScript.productId).then((response) => {
            let data = JSON.parse(response.data.data);
            sceneScript.thingsModel = formatArrayIndex(data);
            // 过滤监测数据和只读数据
            // if (sceneScript.thingsModel.properties) {
            //     sceneScript.thingsModel.properties = sceneScript.thingsModel.properties.filter((item: any) => item.isMonitor == 0 && item.isReadonly == 0);
            //     for (let i = 0; i < sceneScript.thingsModel.properties.length; i++) {
            //         if (sceneScript.thingsModel.properties[i].datatype.params) {
            //             sceneScript.thingsModel.properties[i].datatype.params = sceneScript.thingsModel.properties[i].datatype.params.filter((item: any) => item.isReadonly == 0);
            //         }
            //     }
            // }
            // if (sceneScript.thingsModel.functions) {
            //     sceneScript.thingsModel.functions = sceneScript.thingsModel.functions.filter((item: any) => item.isReadonly == 0);
            //     for (let i = 0; i < sceneScript.thingsModel.functions.length; i++) {
            //         if (sceneScript.thingsModel.functions[i].datatype.params) {
            //             sceneScript.thingsModel.functions[i].datatype.params = sceneScript.thingsModel.functions[i].datatype.params.filter((item: any) => item.isReadonly == 0);
            //         }
            //     }
            // }

            let sceneScripts = [];
            if (sceneScript.type == 1) {
                // 属性
              if (sceneScript.thingsModel.properties) {
                sceneScript.thingsModel.properties = sceneScript.thingsModel.properties.filter((item: any) => item.type == 1 && item.subType == 3);
              }
                sceneScripts = sceneScript.thingsModel.properties;
            } else if (sceneScript.type == 2) {
                // 功能
                sceneScripts = sceneScript.thingsModel.functions;
            }
            // 父级物模型和物模型赋值
            setParentAndModelData(sceneScript, sceneScripts);
            // 解决数组在界面中不更新问题
            formJson.actions[index] = formJson.actions[index];
        });
    }
}
// 设置父级物模型和物模型值
const setParentAndModelData = (sceneScript: any, sceneScripts: any) => {
    for (let i = 0; i < sceneScripts.length; i++) {
        if (sceneScript.parentId == sceneScripts[i].id) {
            sceneScript.parentModel = sceneScripts[i];
            if (sceneScript.parentModel.datatype.type === 'object') {
                // 对象类型，物模型赋值
                for (let j = 0; j < sceneScript.parentModel.datatype.params.length; j++) {
                    if (sceneScript.id == sceneScript.parentModel.datatype.params[j].id) {
                        sceneScript.model = sceneScript.parentModel.datatype.params[j];
                    }
                }
            } else if (sceneScript.parentModel.datatype.arrayType === 'object' && sceneScript.parentModel.datatype.type === 'array') {
                // 对象数组类型，物模型集合移除索引前缀
                if (sceneScript.id.indexOf('array_') != -1) {
                    sceneScript.id = sceneScript.id.substring(9);
                }
                // 物模型赋值
                for (let j = 0; j < sceneScript.parentModel.datatype.params.length; j++) {
                    if (sceneScript.id == sceneScript.parentModel.datatype.params[j].id) {
                        sceneScript.model = sceneScript.parentModel.datatype.params[j];
                    }
                }
            } else if (sceneScript.parentModel.datatype.arrayType !== 'object' && sceneScript.parentModel.datatype.type === 'array') {
                // 普通数组类型，物模型集合移除索引前缀
                if (sceneScript.id.indexOf('array_') != -1) {
                    sceneScript.id = sceneScript.id.substring(9);
                }
                sceneScript.model = {
                    datatype: {
                        type: sceneScript.parentModel.datatype.arrayType,
                        maxLength: -1,
                        min: -1,
                        max: -1,
                        unit: '无单位',
                    },
                };
            } else {
                // 普通类型
                sceneScript.model = sceneScript.parentModel;
            }
            break;
        }
    }
}
// 触发器源改变事件
const handleTriggerSource = (source: any, index: any) => {
    formJson.triggers[index].deviceCount = 0;
    formJson.triggers[index].productId = '' as any;
    formJson.triggers[index].productName = '';
    formJson.triggers[index].thingsModel = {
        properties: [] as any,
        functions: [] as any,
        events: [] as any,
    };
    formJson.triggers[index].id = '';
    formJson.triggers[index].name = '';
    formJson.triggers[index].value = '';
    formJson.triggers[index].valueA = '';
    formJson.triggers[index].valueB = '';
    formJson.triggers[index].parentId = '';
    formJson.triggers[index].parentName = '';
    formJson.triggers[index].model = {} as any;
    formJson.triggers[index].parentModel = {
        datatype: {
            type: '',
            arrayModel: [] as any,
            arrayType: '',
            params: undefined
        }
    };
    formJson.triggers[index].operator = '';
    formJson.triggers[index].deviceNums = [];
    // 定时
    formJson.triggers[index].timerTimeValue = ''; // 时间
    formJson.triggers[index].timerWeekValue = [1, 2, 3, 4, 5, 6, 7]; // 星期
}
const handleActionSourceChange = (source: any, index: any) => {
    console.log('formJson.actions[index]', formJson.actions[index]);
    formJson.actions[index].deviceCount = 0;
    formJson.actions[index].productId = '' as any;
    formJson.actions[index].productName = '';
    formJson.actions[index].thingsModel = {
        properties: [] as any,
        functions: [] as any,
        events: [] as any,
    };
    formJson.actions[index].id = '';
    formJson.actions[index].name = '';
    formJson.actions[index].value = '';
    formJson.actions[index].valueA = '';
    formJson.actions[index].valueB = '';
    formJson.actions[index].parentId = '';
    formJson.actions[index].parentName = '';
    formJson.actions[index].model = {} as any;
    formJson.actions[index].parentModel = {
        datatype: {
            datatype: {
                type: '' as any
            }
        } as any
    };
    formJson.actions[index].operator = '';
    formJson.actions[index].deviceNums = [];
    // 定时
    formJson.actions[index].timerTimeValue = ''; // 时间
    formJson.actions[index].timerWeekValue = [1, 2, 3, 4, 5, 6, 7] as any; // 星期
}
// 添加触发器
const handleAddTrigger = () => {
    formJson.triggers.push({
        source: 1, //1=设备，2=定时,3=产品
        type: 1, // 类型
        id: '',
        name: '',
        operator: '',
        value: '',
        // between操作符时，值=值A-值B
        valueA: '',
        valueB: '',
        model: {
            datatype: {
                type: '',
                arrayModel: [] as any,
                arrayType: '',
                params: [] as any,
                max: '' as any,
                min: '' as any,
                unit: '' as any,
                trueText: '' as any,
                falseText: '' as any,
                enumList: [] as any,
                maxLength: '' as any,
            }
        } as any,
        parentModel: {
            datatype: {
                type: '',
                arrayModel: [] as any,
                arrayType: ''
            }
        },
        parentId: '', // 物模父级id
        parentName: '',
        arrayIndex: '', // 索引，数组才有
        arrayIndexName: '',
        isAdvance: 0, // 自定义CRON
        cronExpression: '', // cron表达式
        timerTimeValue: '', // 时间
        timerWeekValue: [1, 2, 3, 4, 5, 6, 7], // 星期
        productId: 0,
        productName: '',
        deviceNums: [],
        deviceCount: 0,
        scriptPurpose: 2,
        thingsModel: {
            properties: [] as any,
            functions: [] as any,
            events: [] as any,
        }
    });
}
// 添加动作
const handleAddAction = () => {
    formJson.actions.push({
        source: 1, //1=设备，3=产品，4=告警
        type: 2, // 类型
        id: '',
        name: '',
        value: '',
        model: {
            datatype: {
                type: '',
                arrayModel: [] as any,
                arrayType: '',
                params: [] as any,
                max: '' as any,
                min: '' as any,
                unit: '' as any,
                trueText: '' as any,
                falseText: '' as any,
                enumList: [] as any,
                maxLength: '' as any,
            }
        } as any,
        parentId: '', // 物模id
        parentName: '',
        parentModel: {} as any,
        arrayIndex: '', // 索引，数组才有
        arrayIndexName: '',
        productId: 0,
        productName: '',
        deviceNums: [],
        deviceCount: 0,
        scriptPurpose: 3,
        thingsModel: {
            properties: [] as any as any,
            functions: [] as any as any,
            events: [] as any as any,
        },
        valueA: '',
        valueB: '',
        operator: '',
        timerTimeValue: '',
        timerWeekValue: []
    });
}
// 删除触发器
const handleRemoveTrigger = (index: any) => {
    formJson.triggers.splice(index, 1);
}
// 删除动作
const handleRemoveAction = (index: any) => {
    formJson.actions.splice(index, 1);
}
/** cron表达式按钮操作 */
const handleShowCron = (item: any, index: any) => {
    expression.value = item.cronExpression;
    triggerIndex.value = index;
    openCron.value = true;
}
/** 确定后回传值 */
const crontabFill = (value: any) => {
    formJson.triggers[triggerIndex.value].cronExpression = value;
}
/** 星期改变事件 **/
const weekChange = (data: any, index: any) => {
    gentCronExpression(index);
}
/** 时间改变事件 **/
const timeChange = (data: any, index: any) => {
    gentCronExpression(index);
}
/**自定义cron表达式选项改变事件 */
const customerCronChange = () => { }
/** 生成cron表达式**/
const gentCronExpression = (index: any) => {
    let hour = '00';
    let minute = '00';
    if (formJson.triggers[index].timerTimeValue != null && formJson.triggers[index].timerTimeValue != '') {
        hour = formJson.triggers[index].timerTimeValue.substring(0, 2);
        minute = formJson.triggers[index].timerTimeValue.substring(3);
    }
    let week = '*' as any;
    if (formJson.triggers[index].timerWeekValue.length > 0) {
        week = formJson.triggers[index].timerWeekValue.sort();
    }
    formJson.triggers[index].cronExpression = '0 ' + minute + ' ' + hour + ' ? * ' + week;
}
const handleActionTypeChange = (data: any, index: any) => {
    formJson.actions[index].id = '';
    formJson.actions[index].name = '';
    formJson.actions[index].value = '';
    formJson.actions[index].model = {} as any;
    formJson.actions[index].parentId = '';
    formJson.actions[index].parentName = '';
    formJson.actions[index].arrayIndex = '';
    formJson.actions[index].arrayIndexName = '';
    formJson.actions[index].parentModel = {} as any;
}
// 动作物模型项改变事件
const handleActionParentModelChange = (identifier: any, index: any) => {
    formJson.actions[index].model = {} as any;
    formJson.actions[index].value = '';
    formJson.actions[index].arrayIndex = '';
    formJson.actions[index].arrayIndexName = '';

    let sceneScripts = [];
    if (formJson.actions[index].type == 1) {
        //属性
        sceneScripts = formJson.actions[index].thingsModel?.properties || [];
    } else if (formJson.actions[index].type == 2) {
        //功能
        sceneScripts = formJson.actions[index].thingsModel.functions || [];

    }
    for (let i = 0; i < sceneScripts.length; i++) {
        if (sceneScripts[i].id == identifier) {
            formJson.actions[index].parentName = sceneScripts[i].name;
            formJson.actions[index].parentModel = sceneScripts[i];
            if (sceneScripts[i].datatype.type === 'object') {
                // 对象类型
                formJson.actions[index].id = '';
                formJson.actions[index].name = '';
            } else if (sceneScripts[i].datatype.type === 'array' && sceneScripts[i].datatype.arrayType === 'object') {
                // 对象数组类型
                formJson.actions[index].id = '';
                formJson.actions[index].name = '';
            } else if (sceneScripts[i].datatype.type === 'array' && sceneScripts[i].datatype.arrayType !== 'object') {
                // 普通类型，数组类
                formJson.actions[index].id = sceneScripts[i].id;
                formJson.actions[index].name = sceneScripts[i].name;
                // 用于减少界面判断
                formJson.actions[index].model = {
                    datatype: {
                        type: formJson.actions[index].parentModel.datatype.arrayType,
                        maxLength: -1,
                        min: -1,
                        max: -1,
                        unit: '无单位',
                    } as any,
                };
            } else {
                // 普通类型,不包含数组类型
                formJson.actions[index].id = sceneScripts[i].id;
                formJson.actions[index].name = sceneScripts[i].name;
                formJson.actions[index].model = sceneScripts[i];
            }
            break;
        }
    }
}
// 执行动作物模型数组索引选择
const handleActionIndexChange = (id: any, index: any) => {
    formJson.actions[index].arrayIndexName = formJson.actions[index].parentModel.datatype.arrayModel.find((x: any) => x.id == id).name;
    formJson.actions[index].value = '';
    formJson.actions[index].valueA = '';
    formJson.actions[index].valueB = '';
    formJson.actions[index].operator = '';
    // 数组类型保留id和name
    if (formJson.actions[index].parentModel.datatype.arrayType === 'object') {
        formJson.actions[index].id = '';
        formJson.actions[index].name = '';
    }
}
// 执行动作物模型选择
const handleActionModelChange = (id: any, index: any) => {
    formJson.actions[index].operator = '';
    formJson.actions[index].value = '';
    let model = null;
    if (formJson.actions[index].parentModel.datatype.type === 'array' || formJson.actions[index].parentModel.datatype.type === 'object') {
        model = formJson.actions[index].parentModel.datatype.params.find((item: any) => item.id == id);
        formJson.actions[index].name = model.name;
        formJson.actions[index].model = model;
    }
}
// 清空弹框
const resetState = () => {
    state.ruleForm = { ...initialState.ruleForm }
    // state.dialog = { ...initialState.dialog }
    formJson.triggers = [
        {
            productId: 0,
            productName: '',
            deviceCount: 0,
            deviceNums: [],
            source: 1, //1=设备，2=定时，3=产品
            type: 1, // 类型1=属性/2=功能，3=事件
            parentId: '', // 物模父级id
            parentName: '',
            parentModel: {
                datatype: {
                    type: '',
                    arrayModel: [] as any,
                    arrayType: '',
                    params: [] as any,
                    maxLength: -1 as any,
                }
            } as any,
            model: {
                datatype: {
                    type: '',
                    arrayModel: [] as any,
                    arrayType: '',
                    params: [] as any,
                    max: '' as any,
                    min: '' as any,
                    unit: '' as any,
                    trueText: '' as any,
                    falseText: '' as any,
                    enumList: [] as any,
                    maxLength: '' as any,
                }
            },
            operator: '',
            id: '',
            name: '',
            value: '', // between操作符时，值=值A-值B
            valueA: '',
            valueB: '',
            arrayIndex: '', // 索引，数组才有
            arrayIndexName: '',
            isAdvance: 0, // 自定义CRON
            cronExpression: '', // cron表达式
            timerTimeValue: '', // 时间
            timerWeekValue: [1, 2, 3, 4, 5, 6, 7], // 星期
            scriptPurpose: 3, // scriptPurpose:脚本用途(1=数据流，2=触发器，3=执行动作),
            thingsModel: {
                properties: [] as any,
                functions: [] as any as any,
                events: [] as any as any,
            }, // 添加 thingsModel 属性
        },
    ]
    formJson.actions = [
        {
            productId: 0,
            productName: '',
            deviceCount: 0,
            deviceNums: [],
            source: 1,
            type: 2,
            parentId: '',
            parentName: '',
            parentModel: {
                datatype: {
                    type: '',
                    arrayModel: [] as any,
                    arrayType: '',
                    params: [] as any,
                    max: '' as any,
                    min: '' as any,
                    unit: '' as any,
                    trueText: '' as any,
                    falseText: '' as any,
                    enumList: [] as any,
                    maxLength: '' as any,

                }
            },
            model: {
                datatype: {
                    type: '',
                    arrayModel: [] as any,
                    arrayType: '',
                    params: [] as any,
                    max: '' as any,
                    min: '' as any,
                    unit: '' as any,
                    trueText: '' as any,
                    falseText: '' as any,
                    enumList: [] as any,
                    maxLength: '' as any,
                }
            },
            id: '',
            name: '',
            value: '',
            arrayIndex: '',
            arrayIndexName: '',
            scriptPurpose: 3,
            thingsModel: {
                properties: [] as any,
                functions: [] as any,
                events: [] as any,
            },
            valueA: '', // Add valueA property
            valueB: '', // Add valueB property
            operator: '',
            timerTimeValue: '',
            timerWeekValue: []

        }
    ]
    updateBtnDisabled.value = false
};
//打开选择设备弹窗
const handleSelectDevice = (type: any, item: any, index: any) => {
    currentType.value = type
    currentIndex.value = index;
    // 刷新子组件
    DeviceListRef.value.state.tableData.param.pageNum = 1;
    DeviceListRef.value.selectDeviceNums = item.deviceNums;
    DeviceListRef.value.state.tableData.param.productId = item.productId != 0 ? item.productId : '';
    DeviceListRef.value.productId = item.productId;
    DeviceListRef.value.state.tableData.param.productName = item.productName;
    DeviceListRef.value.openDeviceList();
}
//打开选择产品弹窗
const handleSelectProduct = (type: any, item: any, index: any) => {
    currentType.value = type
    currentIndex.value = index;
    ProductListRef.value.openProductList();
}
// 关闭弹窗
const closeDialog = () => {
    state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
    closeDialog();
};
// 物模型格式化处理
const formatArrayIndex = (data: any) => {
    let obj = { ...data };

    for (let o in obj) {
        obj[o] = obj[o].map((item: any) => {
            if (item.datatype.type === 'array') {
                let arrayModel = [];
                for (let k = 0; k < item.datatype.arrayCount; k++) {
                    let index = k > 9 ? String(k) : '0' + k;
                    if (item.datatype.arrayType === 'object') {
                        arrayModel.push({
                            id: index,
                            name: item.name + ' ' + (k + 1),
                        });
                    } else {
                        arrayModel.push({
                            id: index,
                            name: item.name + ' ' + (k + 1),
                        });
                    }
                }
                item.datatype.arrayModel = arrayModel;
            }
            return item;
        });
    }

    return obj;
}
// 触发器类型改变事件
const handleTriggerTypeChange = (source: any, index: any) => {
    formJson.triggers[index].id = '';
    formJson.triggers[index].name = '';
    formJson.triggers[index].model = {} as any;
    formJson.triggers[index].operator = '';
    formJson.triggers[index].value = '';
    formJson.triggers[index].valueA = '';
    formJson.triggers[index].valueB = '';
    formJson.triggers[index].parentId = '';
    formJson.triggers[index].parentName = '';
    formJson.triggers[index].parentModel = {
        datatype: {
            type: '',
            arrayModel: [] as any,
            arrayType: ''
        }
    };
    formJson.triggers[index].arrayIndex = '';
    formJson.triggers[index].arrayIndexName = '';
}
// 触发器父级物模型选择
const handleTriggerParentModelChange = (identifier: any, index: any) => {
    formJson.triggers[index].operator = '';
    formJson.triggers[index].value = '';
    formJson.triggers[index].valueA = '';
    formJson.triggers[index].valueB = '';
    formJson.triggers[index].arrayIndex = '';
    formJson.triggers[index].arrayIndexName = '';
    formJson.triggers[index].model = {} as any;
    console.log(formJson.triggers, 'formJson.triggers');

    let sceneScripts = [];
    if (formJson.triggers[index].type == 1) {
        // 属性
        sceneScripts = formJson.triggers[index].thingsModel.properties

    } else if (formJson.triggers[index].type == 2) {
        //功能
        sceneScripts = formJson.triggers[index].thingsModel.functions;
    } else if (formJson.triggers[index].type == 3) {
        //事件
        sceneScripts = formJson.triggers[index].thingsModel.events;
    }
    // 物模型赋值
    for (let i = 0; i < sceneScripts.length; i++) {
        if (sceneScripts[i].id == identifier) {
            formJson.triggers[index].parentName = sceneScripts[i].name;
            formJson.triggers[index].parentModel = sceneScripts[i];
            if (sceneScripts[i].datatype.type === 'object') {
                // 对象类型
                formJson.triggers[index].id = '';
                formJson.triggers[index].name = '';
            } else if (sceneScripts[i].datatype.type === 'array' && sceneScripts[i].datatype.arrayType === 'object') {
                // 对象数组类型
                formJson.triggers[index].id = '';
                formJson.triggers[index].name = '';
            } else if (sceneScripts[i].datatype.type === 'array' && sceneScripts[i].datatype.arrayType !== 'object') {
                // 普通类型，数组类
                formJson.triggers[index].id = sceneScripts[i].id;
                formJson.triggers[index].name = sceneScripts[i].name;
                // 用于减少界面判断
                formJson.triggers[index].model = {
                    datatype: {
                        type: formJson.triggers[index].parentModel.datatype.arrayType,
                        maxLength: -1,
                        min: -1,
                        max: -1,
                        unit: '无单位',
                    } as any,
                };
            } else {
                // 普通类型,不包含数组类型
                formJson.triggers[index].id = sceneScripts[i].id;
                formJson.triggers[index].name = sceneScripts[i].name;
                formJson.triggers[index].model = sceneScripts[i];
            }
            break;
        }
    }
}
// 触发器-物模数组索引选择
const handleTriggerIndexChange = (id: any, index: any) => {
    formJson.triggers[index].arrayIndexName = formJson.triggers[index].parentModel.datatype.arrayModel.find((x: any) => x.id == id).name;
    formJson.triggers[index].value = '';
    formJson.triggers[index].valueA = '';
    formJson.triggers[index].valueB = '';
    formJson.triggers[index].operator = '';
    // 数组类型保留id和name
    if (formJson.triggers[index].parentModel.datatype.arrayType === 'object') {
        formJson.triggers[index].id = '';
        formJson.triggers[index].name = '';
    }
}
// 触发器物模选择
const handleTriggerModelChange = (id: any, index: any) => {
    formJson.triggers[index].operator = '';
    formJson.triggers[index].value = '';
    formJson.triggers[index].valueA = '';
    formJson.triggers[index].valueB = '';
    let model = null;
    if (formJson.triggers[index].parentModel.datatype.type === 'array' || formJson.triggers[index].parentModel.datatype.type === 'object') {
        model = formJson.triggers[index].parentModel.datatype.params.find((item: any) => item.id == id);
        formJson.triggers[index].name = model.name;
        formJson.triggers[index].model = model;
    }
}
const getSelectProductDevice = (data: any, type: any) => {
    console.log(data, 'data');
    console.log(type, 'types');

    if (currentType.value == null) {
        return;
    }
    if (currentType.value == 'trigger') {
        if (type == 1) {
            formJson.triggers[currentIndex.value].deviceNums = data.deviceNums;
            formJson.triggers[currentIndex.value].deviceCount = data.deviceNums.length;
            formJson.triggers[currentIndex.value].productId = data.productId;
            formJson.triggers[currentIndex.value].productName = data.productName;
        } else if (type == 2) {
            formJson.triggers[currentIndex.value].deviceNums = [];
            formJson.triggers[currentIndex.value].deviceCount = 0;
            formJson.triggers[currentIndex.value].productId = data.productId;
            formJson.triggers[currentIndex.value].productName = data.productName;
        }

        // 获取物模型
        cacheJsonThingsModel(data.productId).then((response) => {
            let data = JSON.parse(response.data.data);
            formJson.triggers[currentIndex.value].thingsModel = formatArrayIndex(data);

            // 清楚之前数据
            formJson.triggers[currentIndex.value].type = 1;
            formJson.triggers[currentIndex.value].parentId = '';
            formJson.triggers[currentIndex.value].parentName = '';
            formJson.triggers[currentIndex.value].parentModel = {
                datatype: {
                    type: '',
                    arrayModel: [] as any,
                    arrayType: ''
                }
            };
            formJson.triggers[currentIndex.value].model = {} as any;
            formJson.triggers[currentIndex.value].operator = '';
            formJson.triggers[currentIndex.value].id = '';
            formJson.triggers[currentIndex.value].name = '';
            formJson.triggers[currentIndex.value].value = '';
            formJson.triggers[currentIndex.value].arrayIndex = '';
            formJson.triggers[currentIndex.value].arrayIndexName = '';
            if (formJson.triggers[currentIndex.value].thingsModel.properties) {
                formJson.triggers[currentIndex.value].thingsModel.properties = formJson.triggers[currentIndex.value].thingsModel.properties.filter((item: any) => item.type == 1 && item.subType == 3);
                // formJson.triggers[currentIndex.value].thingsModel.properties = formJson.triggers[currentIndex.value].thingsModel.properties.filter((item: any) => item.isMonitor == 0 && item.isReadonly == 0);


            }
            // 解决数组在界面中不更新问题
            // this.$set(formJson.triggers, currentIndex.value, formJson.triggers[currentIndex.value]);
            formJson.triggers[currentIndex.value] = formJson.triggers[currentIndex.value];

        });
    } else if (currentType.value == 'action') {
        if (type == 1) {
            formJson.actions[currentIndex.value].deviceNums = data.deviceNums;
            formJson.actions[currentIndex.value].deviceCount = data.deviceNums.length;
            formJson.actions[currentIndex.value].productId = data.productId;
            formJson.actions[currentIndex.value].productName = data.productName;
        } else if (type == 2) {
            formJson.actions[currentIndex.value].deviceNums = [];
            formJson.actions[currentIndex.value].deviceCount = 0;
            formJson.actions[currentIndex.value].productId = data.productId;
            formJson.actions[currentIndex.value].productName = data.productName;
        }

        // 获取物模型
        cacheJsonThingsModel(data.productId).then((response: any) => {
            let data = JSON.parse(response.data.data);
            formJson.actions[currentIndex.value].thingsModel = formatArrayIndex(data);
            console.log(formJson.actions, 'formJson.actions');
            // 清楚之前数据
            formJson.actions[currentIndex.value].type = 1;
            formJson.actions[currentIndex.value].parentId = '';
            formJson.actions[currentIndex.value].parentModel = {} as any;
            formJson.actions[currentIndex.value].parentName = '';
            formJson.actions[currentIndex.value].model = {} as any;
            formJson.actions[currentIndex.value].operator = '';
            formJson.actions[currentIndex.value].id = '';
            formJson.actions[currentIndex.value].name = '';
            formJson.actions[currentIndex.value].value = '';
            formJson.actions[currentIndex.value].arrayIndex = '';
            formJson.actions[currentIndex.value].arrayIndexName = '';


            // 过滤监测数据和只读数据
            if (formJson.actions[currentIndex.value].thingsModel.properties) {
                formJson.actions[currentIndex.value].thingsModel.properties = formJson.actions[currentIndex.value].thingsModel.properties.filter((item: any) => item.type == 1 && item.subType == 3);
                // formJson.actions[currentIndex.value].thingsModel.properties = formJson.actions[currentIndex.value].thingsModel.properties.filter((item: any) => item.isMonitor == 0 && item.isReadonly == 0);
                for (let i = 0; i < formJson.actions[currentIndex.value].thingsModel.properties.length; i++) {
                    if (formJson.actions[currentIndex.value].thingsModel.properties[i].datatype.params) {
                        formJson.actions[currentIndex.value].thingsModel.properties[i].datatype.params = formJson.actions[currentIndex.value].thingsModel.properties[i].datatype.params.filter(
                            (item: any) => item.isMonitor == 0 && item.isReadonly == 0
                        );
                    }
                }

            }
            if (formJson.actions[currentIndex.value].thingsModel.functions) {
                // formJson.actions[currentIndex.value].thingsModel.functions = formJson.actions[currentIndex.value].thingsModel.functions.filter((item: any) => item.type == 1 && item.subType == 3);
                formJson.actions[currentIndex.value].thingsModel.functions = formJson.actions[currentIndex.value].thingsModel.functions.filter((item: any) => item.isReadonly == 0);
                for (let i = 0; i < formJson.actions[currentIndex.value].thingsModel.functions.length; i++) {
                    if (formJson.actions[currentIndex.value].thingsModel.functions[i].datatype.params) {
                        formJson.actions[currentIndex.value].thingsModel.functions[i].datatype.params = formJson.actions[currentIndex.value].thingsModel.functions[i].datatype.params.filter(
                            (item: any) => item.isMonitor == 0 && item.isReadonly == 0
                        );
                    }
                }
            }
            // 解决数组在界面中不更新问题
            // this.$set(formJson.actions, currentIndex.value, formJson.actions[currentIndex.value]);
            formJson.actions[currentIndex.value] = formJson.actions[currentIndex.value];


        });

    }
    console.log(formJson, 'formJson')
}



// 暴露变量
defineExpose({
    openDialog,
});
</script>
<style lang="scss" scoped>
.line {
    height: 1px;
    border-top: 1px solid #dcdfe6;
}

.scene-wrap {
    padding: 6px;

    .search-wrap {
        margin-bottom: 6px;
    }

    .content-wrap {
        padding-bottom: 100px;
    }
}

.scene-config-dialog {

    .condition-wrap,
    .action-wrap {
        position: relative;

        .trigger-type-wrap {
            margin-bottom: 10px;

          :deep(.el-input__inner) {
            /**border: none;*/
            box-shadow: none;
          }
        }

        .item-wrap {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #d9e5f6;
            border-radius: 5px;
            width: 100%;

            .delete-wrap {
                position: absolute;
                right: 10px;
                top: 0;
            }
        }
    }
}

.item-wrap {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #d9e5f6;
    border-radius: 5px;
    width: 100%;

    .delete-wrap {
        position: absolute;
        right: 10px;
        top: 0;
    }
}
</style>