<template>
	<div class="system-role-index-container layout-pd">
		<el-card shadow="hover">
			<div class="system-role-index-search mb15">
				<el-form :model="queryParams" ref="queryForm" size="default" :inline="true" v-show="showSearch">
					<el-form-item label="角色编码" prop="roleCode">
						<el-input v-model="queryParams.roleCode" placeholder="请输入角色编码" clearable />
					</el-form-item>
					<el-form-item label="状态">
						<el-select v-model="queryParams.status" placeholder="配置状态" clearable style="width: 240px">
							<el-option v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictLabel"
								:value="dict.dictValue" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button size="default" type="primary" class="ml10" @click="getTableData">
							<el-icon>
								<ele-Search />
							</el-icon>
							查询
						</el-button>
						<el-button size="default" @click="resetQuery">
							<el-icon><ele-Refresh /></el-icon>
							重置
						</el-button>
					</el-form-item>
				</el-form>
				<el-row :gutter="10" class="mb8" :justify="'space-between'">
					<div>
						<el-button v-auths="['system:roleIndex:add']" size="default" type="primary" class="ml5"
							@click="onOpenAdd">
							<el-icon><ele-Plus /></el-icon>
							新增角色首页配置
						</el-button>
					</div>
					<right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
						@queryTable="getTableData"></right-toolbar>
				</el-row>
			</div>
			<el-table :data="state.tableData.data" v-loading="state.tableData.loading"
				style="width: 100%" border
				:header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
				<el-table-column prop="roleCode" label="角色编码" width="120" align="center"></el-table-column>
				<el-table-column prop="url" label="首页URL" width="200" align="center" :show-overflow-tooltip="true"></el-table-column>
				<el-table-column prop="component" label="组件路径" width="200" align="center" :show-overflow-tooltip="true"></el-table-column>
				<el-table-column prop="priority" label="优先级" width="100" align="center"></el-table-column>
				<el-table-column prop="status" label="状态" width="100" align="center">
					<template #default="scope">
						<DictTag :options="statuslist" :value="scope.row.status"></DictTag>
					</template>
				</el-table-column>
				<el-table-column label="创建时间" prop="createTime" align="center" width="200">
					<template #default="scope">
						<span>{{ scope.row.createTime }}</span>
					</template>
				</el-table-column>
				<el-table-column label="操作" show-overflow-tooltip align="center" class-name="small-padding fixed-width">
					<template #default="scope">
						<el-button v-auths="['system:roleIndex:edit']" size="default" text type="primary"
							@click="onOpenEdit(scope.row)"><el-icon><ele-EditPen /></el-icon>修改</el-button>
						<el-button class="ml15" v-auths="['system:roleIndex:remove']" size="default" text type="primary"
							@click="onTabelRowDel(scope.row)"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination
				v-show="state.tableData.total > 0"
				:total="state.tableData.total"
				v-model:page="queryParams.pageNum"
				v-model:limit="queryParams.pageSize"
				@pagination="getTableData"
			/>
		</el-card>
		<RoleIndexDialog ref="roleIndexDialogRef" @refresh="getTableData()" />
	</div>
</template>

<script setup lang="ts" name="systemRoleIndex">
import { defineAsyncComponent, ref, onMounted, reactive } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getRoleIndexList, deleteRoleIndex } from "/@/api/system/roleIndex";
import { useDictStore } from '/@/stores/dictStore';
import DictTag from '/@/components/DictTag/index.vue';

// 引入组件
const RoleIndexDialog = defineAsyncComponent(() => import('/@/views/system/roleIndex/dialog.vue'));
const Pagination = defineAsyncComponent(() => import('/@/components/Pagination/index.vue'));

const dictStore = useDictStore();

interface statusOption {
	dictValue: string;
	dictLabel: string;
	listClass: string;
	cssClass: string;
}

const roleIndexDialogRef = ref();
const state = reactive({
	tableData: {
		data: [] as any,
		total: 0,
		loading: true,
	},
});

// 搜索内容
let queryParams = reactive({
	roleCode: undefined,
	status: '',
	pageNum: 1,
	pageSize: 10
});

// 状态列表
const statuslist = ref<statusOption[]>([]);
// 显示搜索条件
const showSearch = ref(true);

// 获取表格数据
const getTableData = () => {
	state.tableData.loading = true;
	getRoleIndexList(queryParams).then(response => {
		state.tableData.data = response.data.rows;
		state.tableData.total = response.data.total;
		setTimeout(() => {
			state.tableData.loading = false;
		}, 500);
	});
};

/** 重置按钮操作 */
const resetQuery = () => {
	queryParams.roleCode = undefined;
	queryParams.status = '';
	queryParams.pageNum = 1;
	queryParams.pageSize = 10;
	getTableData();
};

// 获取状态数据
const getdictdata = async () => {
	try {
		statuslist.value = await dictStore.fetchDict('sys_normal_disable');
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
};

// 打开新增弹窗
const onOpenAdd = () => {
	roleIndexDialogRef.value.openDialog('add');
};

// 打开编辑弹窗
const onOpenEdit = (row: any) => {
	roleIndexDialogRef.value.openDialog('edit', row);
};

// 删除当前行
const onTabelRowDel = (row: any) => {
	ElMessageBox.confirm(`此操作将永久删除该配置, 是否继续?`, '提示', {
		confirmButtonText: '删除',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		try {
			await deleteRoleIndex(row.id);
			await getTableData();
			ElMessage.success('删除成功');
		} catch (error) {
			ElMessage.error('删除失败');
		}
	}).catch(() => {
		// 取消删除，不做任何操作
	});
};

// 页面加载时
onMounted(() => {
	getTableData();
	getdictdata();
});
</script>
