import request from '/@/utils/request'

// 查询角色列表
export function listRole(query: Record<string, any>): Promise<any> {
  return request({
    url: '/system/role/list',
    method: 'get',
    params: query
  })
}

// 查询角色详细
export function getRole(roleId: string | number): Promise<any> {
  return request({
    url: `/system/role/${roleId}`,
    method: 'get'
  })
}

// 新增角色
export function addRole(data: Record<string, any>): Promise<any> {
  return request({
    url: '/system/role',
    method: 'post',
    data: data
  })
}

// 修改角色
export function updateRole(data: Record<string, any>): Promise<any> {
  return request({
    url: '/system/role',
    method: 'put',
    data: data
  })
}

// 角色数据权限
export function dataScope(data: Record<string, any>): Promise<any> {
  return request({
    url: '/system/role/dataScope',
    method: 'put',
    data: data
  })
}

// 角色状态修改
export function changeRoleStatus(roleId: string | number, status: string): Promise<any> {
  const data = {
    roleId,
    status
  }
  return request({
    url: '/system/role/changeStatus',
    method: 'put',
    data: data
  })
}

// 删除角色
export function delRole(roleId: string | number): Promise<any> {
  return request({
    url: `/system/role/${roleId}`,
    method: 'delete'
  })
}

// 查询角色已授权用户列表
export function allocatedUserList(query: any): Promise<any> {
  return request({
    url: '/system/role/authUser/allocatedList',
    method: 'get',
    params: query
  })
}

// 查询角色未授权用户列表
export function unallocatedUserList(query: Record<string, any>): Promise<any> {
  return request({
    url: '/system/role/authUser/unallocatedList',
    method: 'get',
    params: query
  })
}

// 取消用户授权角色
export function authUserCancel(data: Record<string, any>): Promise<any> {
  return request({
    url: '/system/role/authUser/cancel',
    method: 'put',
    data: data
  })
}

// 批量取消用户授权角色
export function authUserCancelAll(data: Record<string, any>): Promise<any> {
  return request({
    url: '/system/role/authUser/cancelAll',
    method: 'put',
    params: data
  })
}

// 授权用户选择
export function authUserSelectAll(data: Record<string, any>): Promise<any> {
  return request({
    url: '/system/role/authUser/selectAll',
    method: 'put',
    params: data
  })
}

// 保存或者更新角色首页配置
export function saveOrUpdateRoleIndex(data: Record<string, any>, isUpdate: boolean): Promise<any> {
  const url = isUpdate ? '/sys/sysRoleIndex/edit' : '/sys/sysRoleIndex/add';
  return request({
    url: url,
    method: isUpdate ? 'put': 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 根据角色编码查询首页配置
export function queryIndexByCode(params: { roleCode: string }): Promise<any> {
  return request({
    url: '/sys/sysRoleIndex/queryByCode',
    method: 'get',
    params: params
  })
}

// 根据角色ID查询部门树结构
export function deptTreeSelect(roleId: string | number): Promise<any> {
  return request({
    url: `/system/role/deptTree/${roleId}`,
    method: 'get'
  })
}
