// src/api/gateway/device.ts
import request from '@/utils/request'

const getBaseUrl = () => {
  const host = window.location.hostname
  const port = '5000'
  return `http://${host}:${port}/openApi`
}

// 设备管理
export interface DeviceListParams {
  pageIndex: number
  pageSize: number
  name?: string
  channelName?: string
  pluginName?: string
  pluginType?: number
}

export function getDeviceList(params: DeviceListParams) {
  const query = new URLSearchParams()
  query.append('pageIndex', String(params.pageIndex))
  query.append('pageSize', String(params.pageSize))
  if (params.name) query.append('name', params.name)
  if (params.channelName) query.append('channelName', params.channelName)
  if (params.pluginName) query.append('pluginName', params.pluginName)
  if (params.pluginType !== undefined && params.pluginType !== null) query.append('pluginType', String(params.pluginType))

  const url = `${getBaseUrl()}/runtimeInfo/deviceList?${query.toString()}`
  return request({ url, method: 'get' })
}

export interface DevicePayloadItem {
  id: number
  name: string
  description?: string
  channelId: number
  intervalTime: string
  enable: boolean
  logLevel: 'Trace' | 'Debug' | 'Info' | 'Warn' | 'Error' | 'Fatal'
  devicePropertys?: Record<string, any>
  redundantEnable: boolean
  redundantDeviceId?: number | null
  redundantSwitchType: 0 | 1
  redundantScanIntervalTime: number
  redundantScript?: string
  remark1?: string
  remark2?: string
  remark3?: string
  remark4?: string
  remark5?: string
}

export type ItemChangedType = 'Add' | 'Update'

export function batchSaveDevice(devices: DevicePayloadItem[], type: ItemChangedType, restart = true) {
  const url = `${getBaseUrl()}/control/batchSaveDevice`
  // 将 type 与 restart 置于查询字符串，Body 直接为设备数组
  const query = new URLSearchParams()
  query.append('type', type)
  query.append('restart', String(restart))

  return request({
    url: `${url}?${query.toString()}`,
    method: 'post',
    data: devices
  })
}

export function deleteDevice(ids: number[], restart = true) {
  const base = `${getBaseUrl()}/control/deleteDevice`
  const query = new URLSearchParams()
  query.append('restart', String(restart))
  // Body 直接为 ID 数组
  return request({ url: `${base}?${query.toString()}`, method: 'post', data: ids })
}

export function pauseBusinessThread(id: number, pause: boolean) {
  const url = `${getBaseUrl()}/control/pauseBusinessThread`
  return request({ url, method: 'post', data: { id, pause } })
}

export function restartThread(deviceId: number) {
  const url = `${getBaseUrl()}/control/restartThread`
  return request({ url, method: 'post', data: { deviceId } })
}

export function restartAllThread() {
  const url = `${getBaseUrl()}/control/restartAllThread`
  return request({ url, method: 'post' })
}

export function restartScopeThread() {
  const url = `${getBaseUrl()}/control/restartScopeThread`
  return request({ url, method: 'post' })
}

export function writeVariables(payload: Record<string, Record<string, string>>) {
  const url = `${getBaseUrl()}/control/writeVariables`
  return request({ url, method: 'post', data: payload })
}

// 通道列表（用于下拉）
export interface ChannelListParams {
  pageIndex?: number
  pageSize?: number
  name?: string
  pluginName?: string
  pluginType?: number
}

export function getChannelList(params: ChannelListParams = { pageIndex: 1, pageSize: 1000 }) {
  const query = new URLSearchParams()
  query.append('pageIndex', String(params.pageIndex ?? 1))
  query.append('pageSize', String(params.pageSize ?? 1000))
  if (params.name) query.append('name', params.name)
  if (params.pluginName) query.append('pluginName', params.pluginName)
  if (params.pluginType !== undefined && params.pluginType !== null) query.append('pluginType', String(params.pluginType))

  const url = `${getBaseUrl()}/runtimeInfo/channelList?${query.toString()}`
  return request({ url, method: 'get' })
}

// 插件属性配置
export interface PluginPropertyEditorItem {
  field: string
  label: string
  type: string
  required: boolean
  defaultValue: string
}

export interface PluginPropertiesResponse {
  model: Record<string, any>
  editorItems: PluginPropertyEditorItem[]
  propertyUIType: any
}

export function getPluginProperties(pluginName: string) {
  const url = `${getBaseUrl()}/runtimeInfo/getPluginPropertys`
  return request({
    url,
    method: 'get',
    params: { pluginName }
  })
}
