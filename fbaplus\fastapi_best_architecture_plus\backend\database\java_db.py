#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java系统数据库连接配置
用于连接Java系统的MySQL数据库，查询用户权限等信息
"""

import sys
from typing import AsyncGenerator

from sqlalchemy import URL, text
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, async_sessionmaker, create_async_engine

from backend.common.log import log


class JavaDatabaseConfig:
    """Java系统数据库配置"""
    
    # Java系统MySQL数据库连接信息
    HOST = "*************"
    # HOST = "*************"
    PORT = 5981
    DATABASE = "fastbee5"
    USERNAME = "root"
    PASSWORD = "123456"
    CHARSET = "utf8mb4"


def create_java_database_url() -> URL:
    """
    创建Java系统数据库连接URL
    
    :return: 数据库连接URL
    """
    url = URL.create(
        drivername='mysql+asyncmy',
        username=JavaDatabaseConfig.USERNAME,
        password=JavaDatabaseConfig.PASSWORD,
        host=JavaDatabaseConfig.HOST,
        port=JavaDatabaseConfig.PORT,
        database=JavaDatabaseConfig.DATABASE,
    )
    url = url.update_query_dict({'charset': JavaDatabaseConfig.CHARSET})
    return url


def create_java_async_engine_and_session() -> tuple[AsyncEngine, async_sessionmaker[AsyncSession]]:
    """
    创建Java系统数据库引擎和Session
    
    :return: (engine, session_maker)
    """
    try:
        url = create_java_database_url()
        
        # 数据库引擎
        engine = create_async_engine(
            url,
            echo=False,  # 不输出SQL日志，避免干扰
            echo_pool=False,
            future=True,
            # 较小的连接池，因为主要用于权限查询
            pool_size=5,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=3600,
            pool_pre_ping=True,
            pool_use_lifo=False,
        )
        
        log.info("✅ Java系统数据库连接创建成功")
        
    except Exception as e:
        log.error(f'❌ Java系统数据库连接失败: {e}')
        raise
    else:
        db_session = async_sessionmaker(
            bind=engine,
            class_=AsyncSession,
            autoflush=False,
            expire_on_commit=False,
        )
        return engine, db_session


# 创建Java系统数据库连接
java_engine, java_async_db_session = create_java_async_engine_and_session()


async def get_java_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取Java系统数据库会话
    
    :return: AsyncSession
    """
    async with java_async_db_session() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            log.error(f"Java数据库会话错误: {e}")
            raise
        finally:
            await session.close()


async def test_java_db_connection() -> bool:
    """
    测试Java系统数据库连接
    
    :return: 连接是否成功
    """
    try:
        async with java_async_db_session() as session:
            # 执行简单查询测试连接
            result = await session.execute(text("SELECT 1 as test"))
            test_value = result.scalar()
            
            if test_value == 1:
                log.info("✅ Java系统数据库连接测试成功")
                return True
            else:
                log.error("❌ Java系统数据库连接测试失败: 返回值异常")
                return False
                
    except Exception as e:
        log.error(f"❌ Java系统数据库连接测试失败: {e}")
        return False


if __name__ == "__main__":
    import asyncio
    
    async def main():
        """测试连接"""
        print("🔧 测试Java系统数据库连接...")
        success = await test_java_db_connection()
        if success:
            print("🎉 Java系统数据库连接成功!")
        else:
            print("❌ Java系统数据库连接失败!")
            sys.exit(1)
    
    asyncio.run(main())
