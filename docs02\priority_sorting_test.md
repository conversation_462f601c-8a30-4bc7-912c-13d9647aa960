# 优先级排序功能测试指南

## 测试场景

假设你有以下知识库：
- `pythontest2` - 优先级: 7
- `pythontest3` - 优先级: 8  
- 其他知识库 - 优先级: 0（默认值）或其他数值

## 预期行为

### 1. 降序排列（默认）
当选择"优先级"排序且"降序"时，应该按以下顺序显示：
1. `pythontest3` (优先级: 8) - 最高优先级
2. `pythontest2` (优先级: 7) - 次高优先级  
3. 其他知识库按优先级从高到低排列
4. 优先级为0的知识库在最后

### 2. 升序排列
当选择"优先级"排序且"升序"时，应该按以下顺序显示：
1. 优先级为0的知识库在最前
2. 其他知识库按优先级从低到高排列
3. `pythontest2` (优先级: 7)
4. `pythontest3` (优先级: 8) - 最高优先级在最后

## 测试步骤

### 步骤1：验证默认排序
1. 打开知识库管理页面
2. 确认排序选择器显示"优先级"
3. 确认升序/降序选择器显示"降序"
4. 验证列表顺序：`pythontest3` 应该在 `pythontest2` 前面

### 步骤2：测试升序排序
1. 将升序/降序选择器改为"升序"
2. 验证列表顺序立即改变
3. 验证 `pythontest2` 应该在 `pythontest3` 前面
4. 验证优先级为0的知识库在最前面

### 步骤3：测试降序排序
1. 将升序/降序选择器改回"降序"
2. 验证列表顺序立即改变
3. 验证 `pythontest3` 重新排在 `pythontest2` 前面
4. 验证优先级高的知识库在前面

### 步骤4：验证其他排序方式
1. 选择"创建时间"排序
2. 验证列表按创建时间重新排序
3. 选择"更新时间"排序  
4. 验证列表按更新时间重新排序
5. 重新选择"优先级"排序
6. 验证回到优先级排序

## 调试信息

打开浏览器开发者工具的控制台，应该能看到：
- "应用优先级排序: 降序" 或 "应用优先级排序: 升序"
- "排序后前5项: [...]" 显示排序后的知识库名称和优先级

## 常见问题排查

### 问题1：优先级排序不生效
- 检查控制台是否有错误信息
- 确认知识库确实有不同的优先级值
- 刷新页面重试

### 问题2：升序/降序切换无效
- 检查是否选择了"优先级"排序
- 查看控制台调试信息
- 确认 `handleDescChange` 方法被调用

### 问题3：排序结果不符合预期
- 检查控制台输出的排序结果
- 确认知识库的实际优先级值
- 验证排序逻辑是否正确

## 技术实现说明

### 前端排序逻辑
```javascript
// 降序：高优先级在前
return queryParams.desc ? rankB - rankA : rankA - rankB;

// 当 desc=true (降序)：rankB - rankA
// 如果 rankB > rankA，结果为正数，B排在A前面
// 如果 rankB < rankA，结果为负数，A排在B前面

// 当 desc=false (升序)：rankA - rankB  
// 如果 rankA > rankB，结果为正数，B排在A前面
// 如果 rankA < rankB，结果为负数，A排在B前面
```

### 性能优化
- 优先级排序使用纯前端实现，不调用API
- 只有时间排序才会调用后端API
- 数据缓存在 `originalKnowledgeBases` 中，避免重复请求

## 预期测试结果

✅ **成功指标**：
1. 降序时：pythontest3 (8) 在 pythontest2 (7) 前面
2. 升序时：pythontest2 (7) 在 pythontest3 (8) 前面  
3. 切换升序/降序时列表立即更新
4. 控制台显示正确的调试信息
5. 不会触发额外的API调用

❌ **失败指标**：
1. 排序顺序不正确
2. 升序/降序切换无效
3. 控制台出现错误信息
4. 排序时触发了不必要的API调用

请按照以上步骤测试，并告诉我实际的测试结果！
