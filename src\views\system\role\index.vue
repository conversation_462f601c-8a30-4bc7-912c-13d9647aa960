<template>
	<div class="system-role-container layout-padding">
		<div class="system-role-padding layout-padding-view">
			<div class="system-user-search mb15">
				<el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
					<el-form-item label="角色名称">
						<el-input v-model="state.tableData.param.roleName" clearable aria-label="First Name"
							placeholder="请输入角色名称" style="width: 240px" />
					</el-form-item>
					<el-form-item label="权限字符">
						<el-input v-model="state.tableData.param.roleKey" clearable placeholder="请输入用户权限字符"
							style="width: 240px" />
					</el-form-item>
					<el-form-item label="状态">
						<el-select v-model="state.tableData.param.status" placeholder="状态" clearable
							style="width: 240px">
							<el-option v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictLabel"
								:value="dict.dictValue" />
						</el-select>
					</el-form-item>
					<el-form-item label="创建时间">
						<el-date-picker v-model="dateRange" style="width: 240px" date-format="YYYY-MM-DD"
							value-format="YYYY-MM-DD" type="daterange" range-separator="-" start-placeholder="开始日期"
							end-placeholder="结束日期"></el-date-picker>
					</el-form-item>
					<el-form-item>
						<el-button size="default" @click="handleQuery" type="primary" class="ml10">
							<el-icon>
								<ele-Search />
							</el-icon>
							查询
						</el-button>
						<el-button size="default" @click="resetQuery">
							<el-icon><ele-Refresh /></el-icon>
							重置
						</el-button>
					</el-form-item>
					<!-- <el-button size="default" type="success" class="ml10" @click="onOpenAddRole('add')">
					<el-icon>
						<ele-FolderAdd />
					</el-icon>
					新增角色
				</el-button> -->
				</el-form>
				<el-row :gutter="10" class="mb8" :justify="'space-between'">
					<div>
						<el-button v-auths="['system:role:add']" size="default" type="primary" class="ml5"
							@click="onOpenAddRole">
							<el-icon><ele-Plus /></el-icon>
							新增
						</el-button>
						<el-button v-auths="['system:role:edit']" size="default" type="success" class="ml10"
							:disabled="single" @click="handleUpdate('edit', undefined)">
							<el-icon><ele-EditPen /></el-icon>
							修改
						</el-button>
						<el-button v-auths="['system:role:remove']" size="default" type="danger" class="ml10"
							:disabled="multiple" @click="onRowDel">
							<el-icon><ele-DeleteFilled /></el-icon>
							删除
						</el-button>
						<el-button v-auths="['system:role:export']" size="default" type="warning" class="ml10"
							@click="handleExport">
							<el-icon><ele-Download /></el-icon>
							导出
						</el-button>
					</div>
					<right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
						@queryTable="getTableData"></right-toolbar>
				</el-row>
			</div>
			<el-table :data="state.tableData.data" v-loading="state.tableData.loading" border
				@selection-change="handleSelectionChange" style="width: 100%"
				:header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
				<el-table-column type="selection" />
				<el-table-column prop="roleId" label="角色编号" align="center" />
				<el-table-column prop="roleName" label="角色名称" align="center" show-overflow-tooltip></el-table-column>
				<el-table-column prop="roleKey" label="权限字符" align="center" show-overflow-tooltip></el-table-column>
				<el-table-column prop="roleSort" label="显示顺序" align="center" show-overflow-tooltip></el-table-column>
				<el-table-column prop="status" label="状态" align="center" show-overflow-tooltip width="150">
					<template #default="scope">
						<el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
							@change="handleStatusChange(scope.row)">
						</el-switch>
					</template>
				</el-table-column>
				<!-- <el-table-column prop="describe" label="用户描述" show-overflow-tooltip></el-table-column> -->
				<el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip></el-table-column>
				<el-table-column label="操作" width="220" align="center">
					<template #default="scope">
						<div v-if="scope.row.roleId !== 1">
							<el-button v-auths="['system:role:edit']" :disabled="scope.row.roleId == 1" class="ml15"
								text type="primary"
								@click="handleUpdate('edit', scope.row)"><el-icon><ele-EditPen /></el-icon>修改</el-button>
							<el-button v-auths="['system:role:remove']" :disabled="scope.row.roleId == 1" class="ml15"
								text type="primary"
								@click="onRowDel(scope.row)"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
							<el-dropdown v-auths="['system:role:edit']"
								@command="(command: any) => handleCommand(command, scope.row)">
								<el-button text type="primary" class="ml15"
									:disabled="scope.row.roleId == 1"><el-icon><ele-DArrowRight /></el-icon>更多</el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<div v-auths="['system:role:edit']">
											<el-dropdown-item command="handleDataScope">
												<el-icon>
													<ele-CircleCheck />
												</el-icon>数据权限
											</el-dropdown-item>
										</div>
										<div v-auths="['system:role:edit']">
											<el-dropdown-item
												command="handleAuthUser"><el-icon><ele-User /></el-icon>分配用户</el-dropdown-item>
										</div>
										<div v-auths="['system:role:edit']">
											<el-dropdown-item
												command="handleRoleIndex"><el-icon><ele-HomeFilled /></el-icon>首页配置</el-dropdown-item>
										</div>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
							<!-- <el-dropdown size="small" @command="(command: any) => handleCommand(command, scope.row)"
									>
									<el-button size="small" type="text">更多</el-button>
									<el-dropdown-menu #dropdown>
										<el-dropdown-item command="handleResetPwd" icon="el-icon-key"
											>重置密码</el-dropdown-item>
										<el-dropdown-item command="handleAuthRole" icon="el-icon-circle-check"
											>分配角色</el-dropdown-item>
									</el-dropdown-menu>
								</el-dropdown> -->
						</div>
					</template>
				</el-table-column>
			</el-table>
			<!-- 分配角色数据权限对话框 -->
			<!-- <el-dialog :title="title" v-model="openDataScope" width="500px" append-to-body>
				<el-form :model="state.tableData.ruleForm" label-width="80px">
					<el-form-item label="角色名称">
						<el-input v-model="state.tableData.ruleForm.roleName" :disabled="true" />
					</el-form-item>
					<el-form-item label="权限字符">
						<el-input v-model="state.tableData.ruleForm.roleKey" :disabled="true" />
					</el-form-item>
					<el-form-item label="权限范围">
						<el-select v-model="dataScope" @change="dataScopeSelectChange">
							<el-option v-for="item in dataScopeOptions" :key="item.value" :label="item.label"
								:value="item.value"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="数据权限" v-show="true">
						<el-checkbox v-model="deptExpand"
							@change="handleCheckedTreeExpand($event, 'dept')">展开/折叠</el-checkbox>
						<el-checkbox v-model="deptNodeAll"
							@change="handleCheckedTreeNodeAll($event, 'dept')">全选/全不选</el-checkbox>
						<el-checkbox v-model="state.tableData.ruleForm.deptCheckStrictly"
							@change="handleCheckedTreeConnect($event, 'dept')">父子联动</el-checkbox>
						<el-tree class="tree-border" :data="deptOptions" show-checkbox default-expand-all ref="dept"
							node-key="id" :check-strictly="!state.tableData.ruleForm.deptCheckStrictly"
							empty-text="加载中，请稍候" :props="defaultProps"></el-tree>
					</el-form-item>
				</el-form>
				<div slot="footer" class="dialog-footer">
					<el-button type="primary" @click="submitDataScope">确 定</el-button>
					<el-button @click="cancelDataScope">取 消</el-button>
				</div>
			</el-dialog> -->
			<el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
				style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
				v-model:current-page="state.tableData.param.pageNum" background
				v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
				:total="state.tableData.total">
			</el-pagination>
		</div>
		<RoleDialog ref="roleDialogRef" @refresh="getTableData()" />
		<RoleIndexDialog ref="roleIndexDialogRef" @success="getTableData()" />
	</div>
</template>

<script setup lang="ts" name="systemRole">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { listRole, getRole, delRole, addRole, updateRole, dataScope, changeRoleStatus, deptTreeSelect } from "/@/api/system/role";
import { addDateRange } from '/@/utils/next';
import { download } from '/@/utils/request';
import router from '/@/router';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store

const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const RoleDialog = defineAsyncComponent(() => import('/@/views/system/role/dialog.vue'));
const RoleIndexDialog = defineAsyncComponent(() => import('/@/views/system/role/roleIndexDialog.vue'));

// 定义变量内容
const roleDialogRef = ref();
const roleIndexDialogRef = ref();
const state = reactive<SysRoleState>({
	tableData: {
		data: [],
		ruleForm: {
			roleName: '', // 角色名称
			roleKey: '', // 角色标识
			roleSort: 0, // 排序
			status: '0', // 角色状态
			remark: '', // 角色描述
			roleId: '',
			deptCheckStrictly: true,
			menuCheckStrictly: true,
			menuIds: [] as any,
			deptIds: [] as any,
			createTime: '',
			dataScope: ''
		},
		total: 0,
		loading: false,
		param: {
			pageNum: 1,
			pageSize: 10,
			roleName: undefined,
			roleKey: undefined,
			status: undefined
		},
	},
});
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref() //roleId
interface statusOption {
	dictValue: string;
	dictLabel: string;
}
const statuslist = ref<statusOption[]>([]);
const showSearch = ref(true)    // 显示搜索条件
const dateRange = ref<[string, string]>(['', '']); //时间范围
let deptOptions = ref<TreeType[]>([]);  // 部门树数据

// 初始化表格数据
const getTableData = async () => {
	state.tableData.loading = true;
	try {
		const data = addDateRange(state.tableData.param, dateRange.value)
		const response = await listRole(data);
		state.tableData.data = response.data.rows
		state.tableData.total = response.data.total
	} catch (error) {
		console.error('Error fetching table data:', error);
	} finally {
		setTimeout(() => {
			state.tableData.loading = false;
		}, 500);
	}
};
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
	ids.value = selection.map((item: { roleId: string; }) => item.roleId);
	single.value = selection.length != 1;
	multiple.value = !selection.length;
}
/** 搜索按钮操作 */
const handleQuery = () => {
	state.tableData.param.pageNum = 1;
	getTableData();
}
// 重置按钮
const resetQuery = () => {
	state.tableData.param = {
		pageNum: 1,
		pageSize: 10,
		roleName: undefined,
		roleKey: undefined,
		status: undefined
	}
	dateRange.value = ['', '']
	getTableData();
}
// 获取状态数据
const getdictdata = async () => {
	try {
		statuslist.value = await dictStore.fetchDict('sys_normal_disable')
		// 处理字典数据
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
};
// 更改用户状态
const handleStatusChange = (row: any) => {
	console.log(row.roleId, row, 'row');

	let text = row.status === "0" ? "启用" : "停用";
	ElMessageBox.confirm(`确认要${text}"${row.roleName}"用户吗？，是否继续?`, '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			return changeRoleStatus(row.roleId, row.status).then(() => {
				getTableData();
				ElMessage.success('删除成功');
			});
		})
		.catch((err) => {
			row.status = row.status === "0" ? "1" : "0";
		});
};
// 更多操作触发
const handleCommand = (command: any, row: any) => {
	switch (command) {
		case "handleDataScope":
			handleDataScope('assignment', row);
			break;
		case "handleAuthUser":
			handleAuthUser(row);
			break;
		case "handleRoleIndex":
			handleRoleIndex(row);
			break;
		default:
			break;
	}
}
/** 分配数据权限操作 */
const handleDataScope = async (type: string, row: RowRoleType | undefined) => {
	var roleId = ''
	if (!row) {
		roleId = ids.value
	} else {
		roleId = row.roleId
	}

	roleDialogRef.value.openDialog(type, row, roleId);
};

/** 分配用户操作 */
const handleAuthUser = (row: any) => {
	const roleId = row.roleId;
	router.push(`/system/role-auth/user/${roleId}`);
};

/** 首页配置操作 */
const handleRoleIndex = async (row: any) => {
	// 等待异步组件加载完成
	await nextTick();

	if (roleIndexDialogRef.value && typeof roleIndexDialogRef.value.openDialog === 'function') {
		try {
			roleIndexDialogRef.value.openDialog({ roleCode: row.roleKey });
		} catch (error) {
			ElMessage.error('打开首页配置对话框失败');
		}
	} else {
		ElMessage.error('首页配置功能暂时不可用，请稍后重试');
	}
};
/** 根据角色ID查询部门树结构 */
const getDeptTree = (roleId: string | number) => {
	return deptTreeSelect(roleId).then(response => {
		deptOptions = response.data.depts;
		console.log(response.data.depts, 'depts');


		return response;
	});
}
// 打开新增角色弹窗
const onOpenAddRole = (type: string) => {
	roleDialogRef.value.openDialog(type);
};
// 打开修改角色弹窗
const handleUpdate = (type: string, row: RowRoleType | undefined) => {

	var roleId = ''
	if (!row) {
		roleId = ids.value
	} else {
		roleId = row.roleId
	}
	roleDialogRef.value.openDialog(type, row, roleId);
};
// 删除角色
const onRowDel = (row: RowRoleType) => {
	const roleIds = row.roleId || ids.value;

	// 使用 Element Plus 的消息框确认删除
	ElMessageBox.confirm(`是否确认删除角色编号为"${roleIds}"的数据项？`, '删除确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		// 删除角色 API 调用
		try {
			await delRole(roleIds);
			await getTableData();
			ElMessage.success('删除成功');
		} catch (error) {
			ElMessage.error('删除失败');
		}
	}).catch(() => {
		// 取消删除，不做任何操作
	});
};
/** 导出按钮操作 */
const handleExport = () => {
	const exportParams = {
		pageNum: state.tableData.param.pageNum || 1,
		pageSize: state.tableData.param.pageSize || 10,
		roleName: state.tableData.param.roleName || '',
		roleKey: state.tableData.param.roleKey || '',
		status: state.tableData.param.status || '',
	};
	download('system/role/export', {
		...state.tableData.param
	}, `role_${new Date().getTime()}.xlsx`)
}
// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.param.pageSize = val;
	getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
	state.tableData.param.pageNum = val;
	getTableData();
};
// 页面加载时
onMounted(() => {
	getTableData();
	getdictdata()
});
</script>

<style scoped lang="scss">
.system-role-container {
	.system-role-padding {
		padding: 15px;

		.el-table {
			flex: 1;
		}
	}
}
</style>
