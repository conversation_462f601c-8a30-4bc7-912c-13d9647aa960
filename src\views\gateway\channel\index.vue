<template>
  <div v-auths="['gateway:collect:query']">
    <div class="collect-container">
      <div class="table-header">
        <h2>网关采集管理</h2>
        <el-button type="primary" @click="handleAdd" v-auths="['gateway:collect:add']">
          <el-icon><Plus /></el-icon>
          新增
        </el-button>
      </div>
      
      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="通道名称">
            <el-input 
              v-model="searchForm.name" 
              placeholder="请输入通道名称" 
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="插件名称">
            <el-select 
              v-model="searchForm.pluginName" 
              placeholder="请选择插件名称" 
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="item in pluginOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="插件类型">
            <el-select 
              v-model="searchForm.pluginType" 
              placeholder="请选择插件类型" 
              clearable
              style="width: 150px"
            >
              <el-option label="采集" value="Collect" />
              <el-option label="上传" value="Upload" />
              <el-option label="业务" value="Business" />
            </el-select>
          </el-form-item>
          <el-form-item>
                      <el-button type="primary" @click="handleSearch" v-auths="['gateway:collect:query']">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset" v-auths="['gateway:collect:query']">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <el-table
        :data="tableData"
        stripe
        border
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
        />
        
        <el-table-column
          prop="name"
          label="名称"
          min-width="150"
          show-overflow-tooltip
        />
        
        <el-table-column
          prop="remoteUrl"
          label="远程IP地址"
          min-width="150"
          show-overflow-tooltip
        />
        
        <el-table-column
          prop="bindUrl"
          label="本地绑定IP地址"
          min-width="150"
          show-overflow-tooltip
        />
        
        <el-table-column
          prop="channelType"
          label="通道类型"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="getChannelTypeTag(row)">
              {{ getChannelTypeText(row) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="enable"
          label="启用"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <el-switch
              v-model="row.enable"
              @click="handleStatusChange(row)"
              active-text="启用"
              inactive-text="禁用"
              v-auths="['gateway:collect:edit']"
            />
          </template>
        </el-table-column>
        
        <el-table-column
          label="操作"
          width="200"
          align="center"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
              v-auths="['gateway:collect:edit']"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              v-auths="['gateway:collect:remove']"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        label-position="right"
      >
        <!-- 基础信息 -->
        <div class="form-section">
          <h3 class="section-title">基础信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="名称" prop="name" required>
                <el-input v-model="formData.name" placeholder="请输入名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="插件名称" prop="pluginName" required>
                <el-select v-model="formData.pluginName" placeholder="请选择插件名称" style="width: 100%">
                  <el-option
                    v-for="item in pluginOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="启用">
                <el-switch v-model="formData.enable" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="日志等级">
                <el-select v-model="formData.logLevel" placeholder="请选择日志等级" style="width: 100%">
                  <el-option
                    v-for="item in logLevelOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 连接配置 -->
        <div class="form-section">
          <h3 class="section-title">连接配置</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="通道类型" prop="channelType" required>
                <el-select v-model="formData.channelType" placeholder="请选择通道类型" style="width: 100%">
                  <el-option
                    v-for="item in channelTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="远程URL" prop="remoteUrl" required>
                <el-input v-model="formData.remoteUrl" placeholder="请输入远程URL" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="本地绑定IP地址">
                <el-input v-model="formData.bindUrl" placeholder="请输入本地绑定IP地址" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="缓存超时">
                <el-input-number v-model="formData.cacheTimeout" :min="100" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="连接超时">
                <el-input-number v-model="formData.connectTimeout" :min="100" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大并发数">
                <el-input-number v-model="formData.maxConcurrentCount" :min="1" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="最大连接数">
                <el-input-number v-model="formData.maxClientCount" :min="1" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户端滑动过期时间">
                <el-input-number v-model="formData.checkClearTime" :min="1000" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row> -->
          <!-- <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="心跳内容">
                <div style="display: flex; align-items: center; gap: 10px;">
                  <el-input v-model="formData.heartbeat" placeholder="请输入心跳内容" />
                  <el-checkbox v-model="formData.heartbeatHex">hex</el-checkbox>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="心跳间隔">
                <el-input-number v-model="formData.heartbeatTime" :min="1000" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row> -->
          <!-- <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="DtuId">
                <el-input v-model="formData.dtuId" placeholder="请输入DtuId" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="DtuId是否Hex">
                <el-switch v-model="formData.dtuIdHex" />
              </el-form-item>
            </el-col>
          </el-row> -->
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">
            <el-icon><Close /></el-icon>
            关闭
          </el-button>
          <el-button type="primary" @click="handleSave" v-auths="['gateway:collect:add', 'gateway:collect:edit']">
            <el-icon><Check /></el-icon>
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, Close, Check, Search, Refresh } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 搜索条件
const searchForm = reactive({
  name: '',
  pluginName: '',
  pluginType: ''
})

// 弹窗相关数据
const dialogVisible = ref(false)
const dialogTitle = ref('新建通道')
const formData = ref({})
const formRules = reactive({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' }
  ],
  pluginName: [
    { required: true, message: '请选择插件名称', trigger: 'change' }
  ],
  channelType: [
    { required: true, message: '请选择通道类型', trigger: 'change' }
  ],
  remoteUrl: [
    { required: true, message: '请输入远程URL', trigger: 'blur' }
  ]
})

// 插件选项
const pluginOptions = [
  { label: 'ModbusMaster', value: 'ThingsGateway.Plugin.Modbus.ModbusMaster' },
  { label: 'ModbusSlave', value: 'ThingsGateway.Foundation.Modbus' },
  { label: 'MqttServer', value: 'ThingsGateway.Plugin.Mqtt.MqttServer' },
  { label: 'MqttClient', value: 'ThingsGateway.Plugin.Mqtt.MqttClient' }
] 

// 日志等级选项 - 修复后的映射
const logLevelOptions = [
  { label: 'Trace', value: 0 },
  { label: 'Debug', value: 1 },
  { label: 'Info', value: 2 },
  { label: 'Warning', value: 3 },
  { label: 'Error', value: 4 },
  { label: 'Critical', value: 5 }
]

// 通道类型选项 - 修复后的映射
const channelTypeOptions = [
  { label: 'TCP客户端', value: 0 },    // TcpClient
  { label: 'TCP服务器', value: 1 },    // TcpService  
  { label: '串口', value: 2 },         // SerialPort
  { label: 'UDP', value: 3 },          // UdpSession
  { label: '其他', value: 4 }          // Other
]

// 获取通道类型标签样式
const getChannelTypeTag = (row) => {
  const typeMap = {
    0: 'primary',   // TCP客户端
    1: 'success',   // TCP服务器
    2: 'warning',   // 串口
    3: 'info',      // UDP
    4: 'default'    // 其他
  }
  return typeMap[row.channelType] || 'default'
}

// 获取通道类型文本
const getChannelTypeText = (row) => {
  const typeMap = {
    0: 'TCP客户端',
    1: 'TCP服务器',
    2: '串口',
    3: 'UDP',
    4: '其他'
  }
  return typeMap[row.channelType] || '未知'
}

// 构建查询参数
const buildQueryParams = () => {
  const params = new URLSearchParams()
  
  // 分页参数
  params.append('Current', pagination.currentPage.toString())
  params.append('Size', pagination.pageSize.toString())
  
  // 搜索参数
  if (searchForm.name) {
    params.append('Name', searchForm.name)
  }
  if (searchForm.pluginName) {
    params.append('PluginName', searchForm.pluginName)
  }
  if (searchForm.pluginType) {
    params.append('PluginType', searchForm.pluginType)
  }
  
  // 排序参数（默认按名称升序）
  params.append('SortField', 'Name')
  params.append('SortDesc', 'false')
  
  return params
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const queryParams = buildQueryParams()
    
    // 动态获取当前主机地址，确保同一网段下的其他电脑能访问
    const host = window.location.hostname
    const port = '5000' // 后端服务端口
    const url = `http://${host}:${port}/openApi/runtimeInfo/channelList?${queryParams.toString()}`
    
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result = await response.json()
    
    if (result.code === 200) {
      tableData.value = result.data.records || []
      pagination.total = result.data.total || 0
      pagination.currentPage = result.data.current || 1
      pagination.pageSize = result.data.size || 10
    } else {
      throw new Error(result.msg || '请求失败')
    }
  } catch (error) {
    console.error('加载通道列表失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 新增
const handleAdd = () => {
  dialogVisible.value = true
  dialogTitle.value = '新建通道'
  formData.value = {
    name: '',
    pluginName: 'ThingsGateway.Plugin.Modbus.ModbusMaster',
    enable: true,
    logLevel: 2, // Info
    channelType: 0, // TCP客户端
    remoteUrl: '127.0.0.1:502',
    bindUrl: '',
    cacheTimeout: 500,
    connectTimeout: 3000,
    maxConcurrentCount: 1,
    maxClientCount: 10000,
    checkClearTime: 120000,
    heartbeat: '',
    heartbeatHex: false,
    heartbeatTime: 60000,
    dtuId: '',
    dtuIdHex: false
  }
}

// 编辑
const handleEdit = (row) => {
  dialogVisible.value = true
  dialogTitle.value = '编辑通道'
  
  formData.value = {
    id: row.id,
    name: row.name || '',
    pluginName: row.pluginName || 'ThingsGateway.Plugin.Modbus.ModbusMaster',
    enable: row.enable !== undefined ? row.enable : true,
    logLevel: row.logLevel !== undefined ? row.logLevel : 2,
    channelType: row.channelType !== undefined ? row.channelType : 0,
    remoteUrl: row.remoteUrl || '127.0.0.1:502',
    bindUrl: row.bindUrl || '',
    cacheTimeout: row.cacheTimeout || 500,
    connectTimeout: row.connectTimeout || 3000,
    maxConcurrentCount: row.maxConcurrentCount || 1,
    maxClientCount: row.maxClientCount || 10000,
    checkClearTime: row.checkClearTime || 120000,
    heartbeat: row.heartbeat || '',
    heartbeatHex: row.heartbeatHex || false,
    heartbeatTime: row.heartbeatTime || 60000,
    dtuId: row.dtuId || '',
    dtuIdHex: row.dtuIdHex || false
  }
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除通道 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 动态获取当前主机地址
    const host = window.location.hostname
    const port = '5000'
    const url = `http://${host}:${port}/openApi/control/deleteChannel`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify([row.id])
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result = await response.json()
    
    if (result.code === 200 && result.data === true) {
      ElMessage.success('删除成功')
      loadData()
    } else {
      throw new Error('删除失败')
    }
  } catch (error) {
    if (error.message !== 'cancel') {
      console.error('删除通道失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 状态变更
const handleStatusChange = async (row) => {
  try {
    const requestData = [{
      id: row.id,
      enable: row.enable
    }]
    
    // 动态获取当前主机地址
    const host = window.location.hostname
    const port = '5000'
    const url = `http://${host}:${port}/openApi/control/batchSaveChannel?type=1&restart=true`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result = await response.json()
    
    if (result.code === 200 && result.data === true) {
      ElMessage.success(`${row.name} ${row.enable ? '已启用' : '已禁用'}`)
    } else {
      row.enable = !row.enable
      throw new Error('状态变更失败')
    }
  } catch (error) {
    console.error('状态变更失败:', error)
    ElMessage.error('状态变更失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  loadData()
}

// 重置搜索
const handleReset = () => {
  searchForm.name = ''
  searchForm.pluginName = ''
  searchForm.pluginType = ''
  pagination.currentPage = 1
  loadData()
}

// 分页大小变更
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadData()
}

// 当前页变更
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  loadData()
}

// 表单引用
const formRef = ref()

// 保存表单
const handleSave = async () => {
  try {
    await formRef.value.validate()
    
    // 检查通道名称是否重复（新增时检查）
    if (!formData.value.id) {
      const existingChannel = tableData.value.find(item => item.name === formData.value.name)
      if (existingChannel) {
        ElMessage.error('通道名称已存在，请使用其他名称')
        return
      }
    }
    
    let requestData
    
    if (formData.value.id) {
      // 编辑模式
      requestData = [{
        id: parseInt(formData.value.id),
        name: formData.value.name,
        pluginName: formData.value.pluginName,
        enable: formData.value.enable,
        logLevel: formData.value.logLevel,
        channelType: formData.value.channelType,
        remoteUrl: formData.value.remoteUrl,
        bindUrl: formData.value.bindUrl || '',
        cacheTimeout: formData.value.cacheTimeout,
        connectTimeout: formData.value.connectTimeout,
        maxConcurrentCount: formData.value.maxConcurrentCount,
        maxClientCount: formData.value.maxClientCount,
        checkClearTime: formData.value.checkClearTime,
        heartbeat: formData.value.heartbeat || '',
        heartbeatHex: formData.value.heartbeatHex,
        heartbeatTime: formData.value.heartbeatTime,
        dtuId: formData.value.dtuId || '',
        dtuIdHex: formData.value.dtuIdHex
      }]
    } else {
      // 新增模式
      requestData = [{
        name: formData.value.name,
        pluginName: formData.value.pluginName,
        enable: formData.value.enable,
        logLevel: formData.value.logLevel,
        channelType: formData.value.channelType,
        remoteUrl: formData.value.remoteUrl,
        bindUrl: formData.value.bindUrl || '',
        cacheTimeout: formData.value.cacheTimeout,
        connectTimeout: formData.value.connectTimeout,
        maxConcurrentCount: formData.value.maxConcurrentCount,
        maxClientCount: formData.value.maxClientCount,
        checkClearTime: formData.value.checkClearTime,
        heartbeat: formData.value.heartbeat || '',
        heartbeatHex: formData.value.heartbeatHex,
        heartbeatTime: formData.value.heartbeatTime,
        dtuId: formData.value.dtuId || '',
        dtuIdHex: formData.value.dtuIdHex
      }]
    }
    
    console.log('发送的请求数据:', requestData)
    
    // 动态获取当前主机地址
    const host = window.location.hostname
    const port = '5000'
    const type = formData.value.id ? 1 : 0 // 0=新增, 1=编辑
    const url = `http://${host}:${port}/openApi/control/batchSaveChannel?type=${type}&restart=true`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })
    
    console.log('响应状态:', response.status)
    
    if (!response.ok) {
      const errorText = await response.text()
      console.error('HTTP错误响应:', errorText)
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
    }
    
    const result = await response.json()
    console.log('API返回结果:', result)
    
    if (result.code === 200 && result.data === true) {
      ElMessage.success(formData.value.id ? '编辑成功' : '新增成功')
      dialogVisible.value = false
      loadData()
    } else {
      if (result.code === 500 && result.msg.includes('UNIQUE constraint failed: channel.Name')) {
        ElMessage.error('通道名称已存在，请使用其他名称')
      } else {
        throw new Error(`保存失败: ${result.msg || '未知错误'}`)
      }
    }
  } catch (error) {
    console.error('保存通道失败:', error)
    if (error.message.includes('UNIQUE constraint failed: channel.Name')) {
      ElMessage.error('通道名称已存在，请使用其他名称')
    } else {
      ElMessage.error('保存失败')
    }
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.collect-container {
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-header h2 {
  margin: 0;
  color: #303133;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-button + .el-button) {
  margin-left: 8px;
}

/* 搜索表单样式 */
.search-form {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item:last-child {
  margin-right: 0;
}

/* 弹窗表单样式 */
.form-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-checkbox) {
  margin-left: 8px;
}
</style>