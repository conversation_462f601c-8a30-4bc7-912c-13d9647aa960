# JWT认证问题修复指南

## 🔍 问题诊断

您遇到的401未授权错误是因为前端聊天API调用时没有正确携带JWT token。

## ✅ 已修复的问题

### 1. 前端API调用修复

**问题**: 流式聊天API使用fetch时，Authorization头获取方式错误
```typescript
// 修复前 (错误)
'Authorization': fastApiRequest.defaults.headers.common['Authorization'] || '',

// 修复后 (正确)
'Authorization': getAuthorizationHeader(),
```

**解决方案**: 
- 添加了`getAuthorizationHeader()`函数正确获取token
- 统一所有API调用的认证处理
- 添加了token验证和错误处理

### 2. 认证调试工具

**新增文件**:
- `src/utils/authDebug.ts` - 前端认证调试工具
- `test_auth_debug.py` - 后端认证调试脚本

**功能**:
- 检测所有可能的token存储位置
- 验证token格式和有效性
- 提供详细的调试信息

## 🚀 快速解决步骤

### 步骤1: 确认用户已登录

1. 打开浏览器开发者工具 (F12)
2. 在Console中执行以下命令检查token:
```javascript
// 检查认证状态
authDebugger.printDebugInfo()

// 或者手动检查
console.log('Token:', document.cookie)
console.log('Session:', sessionStorage.getItem('token'))
console.log('Local:', localStorage.getItem('token'))
```

### 步骤2: 运行认证调试

```bash
# 后端认证调试
python test_auth_debug.py

# AI聊天API测试（需要token）
python test_ai_chat.py
```

### 步骤3: 验证修复

1. 确保用户已登录系统
2. 访问聊天页面: http://localhost:3000/#/llm/ai/llm/chat
3. 发送测试消息
4. 检查浏览器控制台是否有认证错误

## 🔧 技术细节

### Token获取逻辑

```typescript
function getAuthorizationHeader(): string {
  const token = Session.get('token');
  if (!token) {
    return '';
  }
  return token.startsWith('Bearer ') ? token : `Bearer ${token}`;
}
```

### 错误处理改进

```typescript
// 统一的认证错误处理
if (error.response?.status === 401) {
  throw new Error('认证失败，请重新登录');
}
```

### 调试信息

在开发环境下，认证失败时会自动打印调试信息:
```typescript
if (import.meta.env.DEV) {
  console.group('🔍 认证错误调试信息');
  AuthDebugger.printDebugInfo();
  console.groupEnd();
}
```

## 🛠️ 故障排除

### 问题1: 仍然出现401错误

**检查清单**:
- [ ] 用户是否已登录？
- [ ] token是否存在于Session/Cookie中？
- [ ] token格式是否正确？
- [ ] token是否已过期？

**解决方法**:
```bash
# 运行认证调试
python test_auth_debug.py

# 在浏览器控制台运行
authDebugger.printDebugInfo()
authDebugger.testToken()
```

### 问题2: Token格式错误

**常见格式**:
- ✅ `Bearer eyJhbGciOiJIUzI1NiIs...`
- ✅ `eyJhbGciOiJIUzI1NiIs...` (会自动添加Bearer)
- ❌ `BearereyJhbGciOiJIUzI1NiIs...` (缺少空格)

### 问题3: Token过期

**检查方法**:
```javascript
// 在浏览器控制台检查token过期时间
const token = Session.get('token');
if (token) {
  const payload = JSON.parse(atob(token.split('.')[1]));
  console.log('Token过期时间:', new Date(payload.exp * 1000));
}
```

### 问题4: 后端认证配置

**检查JWT配置**:
- 确认`backend/common/security/jwt.py`中的认证逻辑
- 验证Java适配器是否正常工作
- 检查Redis中的token数据

## 📝 测试验证

### 1. 手动测试

```bash
# 使用curl测试（替换YOUR_TOKEN）
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8000/api/iot/v1/ai/health
```

### 2. 自动化测试

```bash
# 运行完整测试套件
python test_ai_chat.py

# 只测试认证
python test_auth_debug.py
```

### 3. 前端测试

1. 打开聊天页面
2. 打开开发者工具
3. 发送消息
4. 检查Network标签中的请求头

## 🎯 预防措施

### 1. 定期检查认证状态

```typescript
// 定期验证token有效性
setInterval(async () => {
  try {
    await checkAIHealth();
  } catch (error) {
    if (error.message.includes('认证失败')) {
      // 提示用户重新登录
    }
  }
}, 5 * 60 * 1000); // 每5分钟检查一次
```

### 2. 优雅的错误处理

```typescript
// 统一的认证错误处理
if (error.message?.includes('认证失败') || error.message?.includes('未登录')) {
  ElMessage.error('认证失败，请重新登录后再试');
  // 可选：自动跳转到登录页面
}
```

## 📞 技术支持

如果问题仍然存在，请提供以下信息：

1. **浏览器控制台输出**:
```javascript
authDebugger.printDebugInfo()
```

2. **后端认证测试结果**:
```bash
python test_auth_debug.py
```

3. **网络请求详情**:
- 请求URL
- 请求头（特别是Authorization）
- 响应状态码和内容

4. **用户登录状态**:
- 是否已登录？
- 登录时间
- 用户角色和权限

通过以上步骤，应该能够完全解决JWT认证问题，确保AI聊天功能正常工作。
