import request from '/@/utils/request'

// 查询定时任务调度列表
export function listJob(query: any) {
  return request({
    url: '/iot/job/list',
    method: 'get',
    params: query,
    // 增加超时处理
    timeout: 10000, // 10秒超时
  })
  .catch(error => {
    // 重新抛出错误，让调用者可以处理
    throw error;
  });
}

// 查询定时任务调度详细
export function getJob(jobId: string) {
  return request({
    url: '/iot/job/' + jobId,
    method: 'get',
    timeout: 10000,
  })
  .catch(error => {
    throw error;
  });
}

// 新增定时任务调度
export function addJob(data: any) {
  return request({
    url: '/iot/job',
    method: 'post',
    data: data,
    timeout: 10000,
  })
  .catch(error => {
    throw error;
  });
}

// 修改定时任务调度
export function updateJob(data: any) {
  return request({
    url: '/iot/job',
    method: 'put',
    data: data,
    timeout: 10000,
  })
  .catch(error => {
    throw error;
  });
}

// 删除定时任务调度
export function delJob(jobId: string) {
  return request({
    url: '/iot/job/' + jobId,
    method: 'delete',
    timeout: 10000,
  })
  .catch(error => {
    throw error;
  });
}

// 任务状态修改
export function changeJobStatus(jobId: any, status: any) {
  const data = {
    jobId,
    status
  }
  return request({
    url: '/iot/job/changeStatus',
    method: 'put',
    data: data,
    timeout: 10000,
  })
  .catch(error => {
    throw error;
  });
}

// 定时任务立即执行一次
export function runJob(jobId: any, jobGroup: any) {
  const data = {
    jobId,
    jobGroup
  }
  return request({
    url: '/iot/job/run',
    method: 'put',
    data: data,
    timeout: 10000,
  })
  .catch(error => {
    throw error;
  });
}